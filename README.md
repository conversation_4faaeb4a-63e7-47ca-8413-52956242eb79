# Yas Global Partner - HR Solutions Platform

## نظرة عامة | Overview

**Yas Global Partner** هو موقع شركة متخصصة في حلول الموارد البشرية والتوظيف. يوفر المنصة خدمات شاملة للتوظيف واستشارات الموارد البشرية مع خبرة تزيد عن 10 سنوات في تطوير المواهب وبناء الفرق المتميزة.

**Yas Global Partner** is a comprehensive HR solutions and recruitment platform providing talent acquisition and human resources consulting services with over 10 years of experience in talent development and team building.

## التقنيات المستخدمة | Tech Stack

- **Framework**: Next.js 15.2.4 (React 19)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Package Manager**: PNPM
- **AI Integration**: OpenAI SDK
- **Maps**: Leaflet
- **PDF Export**: jsPDF
- **Charts**: Recharts
- **Forms**: React Hook Form + Zod

## الميزات الرئيسية | Key Features

### 🏠 الصفحة الرئيسية | Homepage
- Hero section with company branding
- Services overview
- About section
- Contact information
- Responsive design with Arabic/English support

### 📊 لوحة التحكم | Dashboard
- **إدارة الوظائف** | Job Management (`/dashboard/jobs`)
- **إدارة المرشحين** | Candidate Management (`/dashboard/candidates`)
- **إدارة العملاء** | Client Management (`/dashboard/clients`)
- **الطلبات** | Applications (`/dashboard/applications`)
- **الوظائف المتاحة** | Available Jobs (`/dashboard/available-jobs`)
- **التقارير** | Reports (`/dashboard/reports`)
- **الإعدادات** | Settings (`/dashboard/settings`)
- **إدارة المستخدمين** | User Management (`/dashboard/users`)
- **الإشعارات** | Notifications (`/dashboard/notifications`)
- **الملف الشخصي** | Profile (`/dashboard/profile`)

### 🤖 الميزات المتقدمة | Advanced Features
- AI-powered chatbot for customer support
- Video calling integration
- PDF export functionality
- Interactive maps for location services
- Multi-language support (Arabic/English)
- Dark/Light theme support
- Mobile-responsive design

## هيكل المشروع | Project Structure

```
├── app/                    # Next.js App Router
│   ├── dashboard/         # Dashboard pages
│   ├── login/            # Authentication
│   ├── globals.css       # Global styles
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Homepage
├── components/           # Reusable components
│   ├── ui/              # UI components (Radix UI)
│   ├── dashboard-layout.tsx
│   ├── hero-section.tsx
│   ├── services-section.tsx
│   └── ...
├── hooks/               # Custom React hooks
├── lib/                 # Utility functions
├── data/               # Static data and types
├── config/             # Configuration files
├── public/             # Static assets
└── styles/             # Additional styles
```

## التثبيت والتشغيل | Installation & Setup

### المتطلبات | Prerequisites
- Node.js 18+ 
- PNPM (recommended) or npm

### خطوات التثبيت | Installation Steps

1. **استنساخ المشروع | Clone the repository**
```bash
git clone <repository-url>
cd yas-global-partner
```

2. **تثبيت التبعيات | Install dependencies**
```bash
pnpm install
# or
npm install
```

3. **تشغيل الخادم المحلي | Start development server**
```bash
pnpm dev
# or
npm run dev
```

4. **فتح المتصفح | Open browser**
```
http://localhost:3000
```

## الأوامر المتاحة | Available Scripts

```bash
pnpm dev          # تشغيل الخادم المحلي | Start development server
pnpm build        # بناء المشروع للإنتاج | Build for production
pnpm start        # تشغيل الخادم الإنتاجي | Start production server
pnpm lint         # فحص الكود | Lint code
```

## البيئة والإعدادات | Environment & Configuration

### متغيرات البيئة | Environment Variables
قم بإنشاء ملف `.env.local` وأضف المتغيرات التالية:

```env
# OpenAI API (for AI chatbot)
OPENAI_API_KEY=your_openai_api_key

# Database (if applicable)
DATABASE_URL=your_database_url

# Authentication (if applicable)
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

## المساهمة | Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## الترخيص | License

This project is private and proprietary to Yas Global Partner.

## الدعم | Support

للدعم التقني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

For technical support or inquiries, please contact the development team.

---

**Yas Global Partner** - Transforming HR with Excellence
