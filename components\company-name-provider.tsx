"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface CompanyNameContextType {
  companyName: string
  updateCompanyName: (name: string) => void
}

const CompanyNameContext = createContext<CompanyNameContextType | undefined>(undefined)

export function CompanyNameProvider({ children }: { children: ReactNode }) {
  const [companyName, setCompanyName] = useState("Yas Global Partner")

  useEffect(() => {
    // تحميل اسم الشركة من localStorage عند بدء التطبيق
    const savedName = localStorage.getItem('companyName')
    if (savedName) {
      setCompanyName(savedName)
      // تحديث عنوان الصفحة
      document.title = `${savedName} - نظام إدارة التوظيف`
    }
  }, [])

  const updateCompanyName = (name: string) => {
    setCompanyName(name)
    localStorage.setItem('companyName', name)
    // تحديث عنوان الصفحة
    document.title = `${name} - نظام إدارة التوظيف`
  }

  return (
    <CompanyNameContext.Provider value={{ companyName, updateCompanyName }}>
      {children}
    </CompanyNameContext.Provider>
  )
}

export function useCompanyName() {
  const context = useContext(CompanyNameContext)
  if (context === undefined) {
    throw new Error('useCompanyName must be used within a CompanyNameProvider')
  }
  return context
}
