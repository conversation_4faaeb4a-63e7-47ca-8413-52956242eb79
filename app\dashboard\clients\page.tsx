"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Search, Edit, Trash2, Eye, Phone, Mail, MapPin, FileText, MessageSquare } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { MapComponent } from "@/components/map-component"

interface Client {
  id: string
  name: string
  company: string
  email: string
  phone: string
  status: "جديد" | "تفاوض" | "موقّع" | "غير مهتم"
  location: string
  industry: string
  contractValue: string
  joinDate: string
  activeJobs: number
  address: string
  lat: number
  lng: number
  notes: string
  documents: string[]
  communicationLog: Array<{
    date: string
    type: "call" | "email" | "meeting"
    notes: string
  }>
}

export default function ClientsPage() {
  const router = useRouter()
  const [clients, setClients] = useState<Client[]>([
    {
      id: "1",
      name: "أحمد محمد",
      company: "شركة التقنية المتقدمة",
      email: "<EMAIL>",
      phone: "+966501234567",
      status: "موقّع",
      location: "الرياض",
      industry: "تقنية المعلومات",
      contractValue: "150,000 ريال",
      joinDate: "2024-01-15",
      activeJobs: 3,
      address: "طريق الملك فهد، الرياض 12345",
      lat: 24.7136,
      lng: 46.6753,
      notes: "عميل مميز، يفضل التواصل عبر البريد الإلكتروني",
      documents: ["عقد الخدمة", "رخصة تجارية"],
      communicationLog: [
        { date: "2024-01-20", type: "call", notes: "مناقشة متطلبات الوظيفة الجديدة" },
        { date: "2024-01-18", type: "email", notes: "إرسال تقرير شهري" },
      ],
    },
    {
      id: "2",
      name: "فاطمة العلي",
      company: "مجموعة الأعمال الذكية",
      email: "<EMAIL>",
      phone: "+966507654321",
      status: "تفاوض",
      location: "جدة",
      industry: "الخدمات المالية",
      contractValue: "200,000 ريال",
      joinDate: "2024-02-20",
      activeJobs: 1,
      address: "شارع التحلية، جدة 21455",
      lat: 21.4858,
      lng: 39.1925,
      notes: "مهتمة بالتوظيف التقني",
      documents: ["اتفاقية أولية"],
      communicationLog: [{ date: "2024-02-22", type: "meeting", notes: "اجتماع لمناقشة الشروط" }],
    },
    {
      id: "3",
      name: "خالد السعد",
      company: "شركة الإنشاءات الحديثة",
      email: "<EMAIL>",
      phone: "+966509876543",
      status: "جديد",
      location: "الدمام",
      industry: "الإنشاءات",
      contractValue: "100,000 ريال",
      joinDate: "2024-03-10",
      activeJobs: 0,
      address: "الكورنيش، الدمام 31952",
      lat: 26.4207,
      lng: 50.0888,
      notes: "عميل جديد، يحتاج متابعة",
      documents: [],
      communicationLog: [],
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [newClient, setNewClient] = useState({
    name: "",
    company: "",
    email: "",
    phone: "",
    status: "جديد" as const,
    location: "",
    industry: "",
    contractValue: "",
    address: "",
    notes: "",
  })

  const filteredClients = clients.filter(
    (client) =>
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddClient = () => {
    const client: Client = {
      id: Date.now().toString(),
      ...newClient,
      joinDate: new Date().toISOString().split("T")[0],
      activeJobs: 0,
      lat: 24.7136 + Math.random() * 0.1,
      lng: 46.6753 + Math.random() * 0.1,
      documents: [],
      communicationLog: [],
    }
    setClients([...clients, client])
    setNewClient({
      name: "",
      company: "",
      email: "",
      phone: "",
      status: "جديد",
      location: "",
      industry: "",
      contractValue: "",
      address: "",
      notes: "",
    })
    setIsAddDialogOpen(false)
  }

  const handleViewClient = (client: Client) => {
    // توجيه إلى صفحة تفاصيل الشركة
    router.push(`/dashboard/clients/${client.id}`)
  }

  const handleEditClient = (client: Client) => {
    setSelectedClient(client)
    setIsEditDialogOpen(true)
  }

  const handleDeleteClient = (clientId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا العميل؟")) {
      setClients(clients.filter((c) => c.id !== clientId))
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "موقّع":
        return "bg-green-100 text-green-800"
      case "تفاوض":
        return "bg-yellow-100 text-yellow-800"
      case "جديد":
        return "bg-blue-100 text-blue-800"
      case "غير مهتم":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const mapLocations = clients.map((client) => ({
    id: client.id,
    name: client.company,
    address: client.address,
    lat: client.lat,
    lng: client.lng,
    type: "client" as const,
    details: {
      company: client.company,
      positions: client.activeJobs,
      description: client.industry,
    },
  }))

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">إدارة العملاء</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">إدارة وتتبع جميع عملائك وحالاتهم</p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-[#C0322D] hover:bg-[#C1352F]">
                <Plus className="h-4 w-4 mr-2" />
                إضافة عميل جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>إضافة عميل جديد</DialogTitle>
                <DialogDescription>أدخل بيانات العميل الجديد</DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">اسم العميل</Label>
                  <Input
                    id="name"
                    value={newClient.name}
                    onChange={(e) => setNewClient({ ...newClient, name: e.target.value })}
                    placeholder="أدخل اسم العميل"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company">اسم الشركة</Label>
                  <Input
                    id="company"
                    value={newClient.company}
                    onChange={(e) => setNewClient({ ...newClient, company: e.target.value })}
                    placeholder="أدخل اسم الشركة"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newClient.email}
                    onChange={(e) => setNewClient({ ...newClient, email: e.target.value })}
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">رقم الهاتف</Label>
                  <Input
                    id="phone"
                    value={newClient.phone}
                    onChange={(e) => setNewClient({ ...newClient, phone: e.target.value })}
                    placeholder="أدخل رقم الهاتف"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">الموقع</Label>
                  <Input
                    id="location"
                    value={newClient.location}
                    onChange={(e) => setNewClient({ ...newClient, location: e.target.value })}
                    placeholder="أدخل الموقع"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="industry">القطاع</Label>
                  <Input
                    id="industry"
                    value={newClient.industry}
                    onChange={(e) => setNewClient({ ...newClient, industry: e.target.value })}
                    placeholder="أدخل القطاع"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contractValue">قيمة العقد</Label>
                  <Input
                    id="contractValue"
                    value={newClient.contractValue}
                    onChange={(e) => setNewClient({ ...newClient, contractValue: e.target.value })}
                    placeholder="أدخل قيمة العقد"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">الحالة</Label>
                  <Select
                    value={newClient.status}
                    onValueChange={(value: any) => setNewClient({ ...newClient, status: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="جديد">جديد</SelectItem>
                      <SelectItem value="تفاوض">تفاوض</SelectItem>
                      <SelectItem value="موقّع">موقّع</SelectItem>
                      <SelectItem value="غير مهتم">غير مهتم</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="address">العنوان الكامل</Label>
                  <Input
                    id="address"
                    value={newClient.address}
                    onChange={(e) => setNewClient({ ...newClient, address: e.target.value })}
                    placeholder="أدخل العنوان الكامل"
                  />
                </div>
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="notes">ملاحظات</Label>
                  <Textarea
                    id="notes"
                    value={newClient.notes}
                    onChange={(e) => setNewClient({ ...newClient, notes: e.target.value })}
                    placeholder="أدخل أي ملاحظات إضافية"
                    rows={3}
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-2 rtl:space-x-reverse mt-6">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  إلغاء
                </Button>
                <Button onClick={handleAddClient} className="bg-[#C0322D] hover:bg-[#C1352F]">
                  إضافة العميل
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Map Section */}
        <Card>
          <CardHeader>
            <CardTitle>خريطة مواقع العملاء</CardTitle>
            <CardDescription>عرض جغرافي لمواقع العملاء</CardDescription>
          </CardHeader>
          <CardContent>
            <MapComponent locations={mapLocations} height="400px" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>قائمة العملاء</CardTitle>
            <CardDescription>جميع العملاء المسجلين في النظام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 rtl:space-x-reverse mb-6">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في العملاء..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>العميل</TableHead>
                  <TableHead>الشركة</TableHead>
                  <TableHead>التواصل</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الوظائف النشطة</TableHead>
                  <TableHead>قيمة العقد</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredClients.map((client) => (
                  <TableRow key={client.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{client.name}</div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {client.location}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{client.company}</div>
                        <div className="text-sm text-gray-500">{client.industry}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-1" />
                          {client.email}
                        </div>
                        <div className="flex items-center text-sm">
                          <Phone className="h-3 w-3 mr-1" />
                          {client.phone}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(client.status)}>{client.status}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{client.activeJobs}</Badge>
                    </TableCell>
                    <TableCell className="font-medium">{client.contractValue}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <Button variant="ghost" size="sm" onClick={() => handleViewClient(client)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleEditClient(client)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600"
                          onClick={() => handleDeleteClient(client.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* View Client Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>تفاصيل العميل: {selectedClient?.name}</DialogTitle>
              <DialogDescription>معلومات شاملة عن العميل</DialogDescription>
            </DialogHeader>
            {selectedClient && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <Label className="text-sm font-medium">الشركة</Label>
                    <p className="text-sm">{selectedClient.company}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">القطاع</Label>
                    <p className="text-sm">{selectedClient.industry}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">الحالة</Label>
                    <Badge className={getStatusColor(selectedClient.status)}>{selectedClient.status}</Badge>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">قيمة العقد</Label>
                    <p className="text-sm">{selectedClient.contractValue}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">معلومات التواصل</Label>
                    <div className="space-y-2 mt-2">
                      <div className="flex items-center text-sm">
                        <Mail className="h-4 w-4 mr-2" />
                        {selectedClient.email}
                      </div>
                      <div className="flex items-center text-sm">
                        <Phone className="h-4 w-4 mr-2" />
                        {selectedClient.phone}
                      </div>
                      <div className="flex items-center text-sm">
                        <MapPin className="h-4 w-4 mr-2" />
                        {selectedClient.address}
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">المستندات</Label>
                    <div className="space-y-2 mt-2">
                      {selectedClient.documents.length > 0 ? (
                        selectedClient.documents.map((doc, index) => (
                          <div key={index} className="flex items-center text-sm">
                            <FileText className="h-4 w-4 mr-2" />
                            {doc}
                          </div>
                        ))
                      ) : (
                        <p className="text-sm text-gray-500">لا توجد مستندات</p>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">ملاحظات</Label>
                  <p className="text-sm mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    {selectedClient.notes || "لا توجد ملاحظات"}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium">سجل التواصل</Label>
                  <div className="space-y-3 mt-2">
                    {selectedClient.communicationLog.length > 0 ? (
                      selectedClient.communicationLog.map((log, index) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              <MessageSquare className="h-4 w-4 mr-2" />
                              <span className="text-sm font-medium">{log.type}</span>
                            </div>
                            <span className="text-xs text-gray-500">{log.date}</span>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300">{log.notes}</p>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">لا يوجد سجل تواصل</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
