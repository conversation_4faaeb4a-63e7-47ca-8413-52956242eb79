"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
  Settings,
  Palette,
  Globe,
  Bot,
  Save,
  RefreshCw,
  DollarSign,
  Plus,
  Edit,
  Trash2,
  Link,
  Upload,
  Zap,
  Calendar,
  MessageSquare,
} from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { useSettings } from "@/hooks/use-settings"
import { useAuth } from "@/hooks/use-auth"
import { useCompanyName } from "@/components/company-name-provider"

export default function SettingsPage() {
  const { settings, updateSettings } = useSettings()
  const { hasPermission } = useAuth()
  const { companyName: globalCompanyName, updateCompanyName } = useCompanyName()
  const [isLoading, setIsLoading] = useState(false)
  const [isWhatsAppDialogOpen, setIsWhatsAppDialogOpen] = useState(false)
  const [isAddModelDialogOpen, setIsAddModelDialogOpen] = useState(false)
  const [isAddIntegrationDialogOpen, setIsAddIntegrationDialogOpen] = useState(false)
  const [selectedColor, setSelectedColor] = useState("#dc2626")
  const [companyName, setCompanyName] = useState(globalCompanyName)
  const [defaultCurrency, setDefaultCurrency] = useState("SAR")
  const [isDarkMode, setIsDarkMode] = useState(false)

  const handleSaveSettings = async () => {
    setIsLoading(true)
    try {
      // تحديث إعدادات النظام
      updateSettings({
        ...settings,
        theme: {
          ...settings.theme,
          primaryColor: selectedColor,
          darkMode: isDarkMode
        },
        companyName: companyName,
        defaultCurrency: defaultCurrency
      })

      // تطبيق التغييرات على الألوان والوضع
      applyColorTheme(selectedColor)
      applyDarkMode(isDarkMode)

      // تحديث العملة واسم الشركة في جميع أنحاء النظام
      localStorage.setItem('defaultCurrency', defaultCurrency)
      localStorage.setItem('primaryColor', selectedColor)
      localStorage.setItem('darkMode', isDarkMode.toString())

      // تحديث اسم الشركة عبر المزود
      updateCompanyName(companyName)

      await new Promise((resolve) => setTimeout(resolve, 1000))
      console.log("Settings saved successfully")
    } catch (error) {
      console.error("Error saving settings:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // تطبيق الألوان على النظام
  const applyColorTheme = (color: string) => {
    const root = document.documentElement

    // تحويل اللون إلى RGB
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)

    // تطبيق الألوان على CSS variables
    root.style.setProperty('--primary', `${r} ${g} ${b}`)
    root.style.setProperty('--primary-foreground', '***********')

    // تحديث جميع العناصر التي تستخدم اللون الأساسي
    const elements = document.querySelectorAll('.bg-red-600, .hover\\:bg-red-700, .text-red-600, .border-red-600')
    elements.forEach(element => {
      const el = element as HTMLElement
      if (el.classList.contains('bg-red-600')) {
        el.style.backgroundColor = color
      }
      if (el.classList.contains('text-red-600')) {
        el.style.color = color
      }
      if (el.classList.contains('border-red-600')) {
        el.style.borderColor = color
      }
    })
  }

  // تطبيق الوضع الليلي/النهاري
  const applyDarkMode = (dark: boolean) => {
    const root = document.documentElement
    if (dark) {
      root.classList.add('dark')
      root.style.setProperty('--background', '0 0% 3.9%')
      root.style.setProperty('--foreground', '0 0% 98%')
      root.style.setProperty('--card', '0 0% 3.9%')
      root.style.setProperty('--card-foreground', '0 0% 98%')
    } else {
      root.classList.remove('dark')
      root.style.setProperty('--background', '0 0% 100%')
      root.style.setProperty('--foreground', '0 0% 3.9%')
      root.style.setProperty('--card', '0 0% 100%')
      root.style.setProperty('--card-foreground', '0 0% 3.9%')
    }
  }

  // إعادة تعيين الإعدادات للافتراضية
  const handleResetSettings = () => {
    const defaultColor = "#dc2626"
    const defaultName = "Yas Global Partner"
    const defaultCurr = "SAR"
    const defaultDark = false

    setSelectedColor(defaultColor)
    setCompanyName(defaultName)
    setDefaultCurrency(defaultCurr)
    setIsDarkMode(defaultDark)

    // تطبيق الإعدادات الافتراضية
    applyColorTheme(defaultColor)
    applyDarkMode(defaultDark)

    // تحديث localStorage
    localStorage.setItem('defaultCurrency', defaultCurr)
    localStorage.setItem('primaryColor', defaultColor)
    localStorage.setItem('darkMode', defaultDark.toString())

    // تحديث اسم الشركة عبر المزود
    updateCompanyName(defaultName)

    updateSettings({
      ...settings,
      theme: {
        ...settings.theme,
        primaryColor: defaultColor,
        darkMode: defaultDark
      },
      companyName: defaultName,
      defaultCurrency: defaultCurr
    })
  }

  // تحديث اسم الشركة
  const handleCompanyNameChange = (e) => {
    setCompanyName(e.target.value)
  }

  // تحديث اللون الأساسي
  const handleColorChange = (color) => {
    setSelectedColor(color)
  }

  if (!hasPermission("settings")) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">غير مصرح</h2>
            <p className="text-gray-600 dark:text-gray-300">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 md:space-y-8 p-4 md:p-6">
        <div>
          <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">الإعدادات</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">قم بتخصيص إعدادات النظام والموقع حسب احتياجاتك</p>
        </div>

        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-6 gap-1">
            <TabsTrigger value="general" className="flex items-center gap-1 text-xs md:text-sm">
              <Settings className="w-3 h-3 md:w-4 md:h-4" />
              <span className="hidden sm:inline">عام</span>
            </TabsTrigger>
            <TabsTrigger value="appearance" className="flex items-center gap-1 text-xs md:text-sm">
              <Palette className="w-3 h-3 md:w-4 md:h-4" />
              <span className="hidden sm:inline">المظهر</span>
            </TabsTrigger>
            <TabsTrigger value="language" className="flex items-center gap-1 text-xs md:text-sm">
              <Globe className="w-3 h-3 md:w-4 md:h-4" />
              <span className="hidden sm:inline">اللغة</span>
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-1 text-xs md:text-sm">
              <Bot className="w-3 h-3 md:w-4 md:h-4" />
              <span className="hidden sm:inline">الذكاء الاصطناعي</span>
            </TabsTrigger>
            <TabsTrigger value="revenue" className="flex items-center gap-1 text-xs md:text-sm">
              <DollarSign className="w-3 h-3 md:w-4 md:h-4" />
              <span className="hidden sm:inline">الإيرادات</span>
            </TabsTrigger>
            <TabsTrigger value="integrations" className="flex items-center gap-1 text-xs md:text-sm">
              <Link className="w-3 h-3 md:w-4 md:h-4" />
              <span className="hidden sm:inline">التكاملات</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>الإعدادات العامة</CardTitle>
                <CardDescription>إعدادات أساسية للنظام</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">معلومات الشركة</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company-name">اسم الشركة</Label>
                      <Input
                        id="company-name"
                        value={companyName}
                        onChange={handleCompanyNameChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company-name-ar">اسم الشركة بالعربية</Label>
                      <Input id="company-name-ar" defaultValue="Yas Global Partner" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-description">وصف الشركة</Label>
                    <Textarea
                      id="company-description"
                      defaultValue="شركة رائدة في مجال التوظيف والاستشارات الإدارية"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company-email">البريد الإلكتروني</Label>
                      <Input id="company-email" type="email" defaultValue="<EMAIL>" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company-phone">رقم الهاتف</Label>
                      <Input id="company-phone" defaultValue="+966 11 234 5678" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company-address">العنوان</Label>
                    <Textarea
                      id="company-address"
                      defaultValue="الرياض، المملكة العربية السعودية"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timezone">المنطقة الزمنية</Label>
                    <Select defaultValue="asia/riyadh">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asia/riyadh">الرياض (GMT+3)</SelectItem>
                        <SelectItem value="asia/dubai">دبي (GMT+4)</SelectItem>
                        <SelectItem value="asia/kuwait">الكويت (GMT+3)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات المظهر</CardTitle>
                <CardDescription>تخصيص شكل ومظهر النظام</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Theme Settings */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">إعدادات السمة</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-0.5">
                        <Label>الوضع الليلي</Label>
                        <p className="text-sm text-gray-500">تبديل بين الوضع الليلي والنهاري</p>
                      </div>
                      <Switch
                        checked={isDarkMode}
                        onCheckedChange={(checked) => {
                          setIsDarkMode(checked)
                          applyDarkMode(checked)
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-0.5">
                        <Label>الوضع الليلي التلقائي</Label>
                        <p className="text-sm text-gray-500">تغيير تلقائي حسب وقت النظام</p>
                      </div>
                      <Switch />
                    </div>
                  </div>
                </div>

                {/* Color Scheme */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">نظام الألوان</h4>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>الألوان المحددة مسبقاً</Label>
                      <div className="grid grid-cols-6 gap-3">
                        {[
                          { name: "أحمر", color: "#dc2626" },
                          { name: "أزرق", color: "#2563eb" },
                          { name: "أخضر", color: "#16a34a" },
                          { name: "بنفسجي", color: "#9333ea" },
                          { name: "برتقالي", color: "#ea580c" },
                          { name: "وردي", color: "#db2777" }
                        ].map((colorOption) => (
                          <button
                            key={colorOption.color}
                            onClick={() => handleColorChange(colorOption.color)}
                            className={`w-12 h-12 rounded-lg border-2 transition-all ${
                              selectedColor === colorOption.color
                                ? 'border-gray-900 scale-110'
                                : 'border-gray-300 hover:border-gray-400'
                            }`}
                            style={{ backgroundColor: colorOption.color }}
                            title={colorOption.name}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="custom-color">لون مخصص</Label>
                      <div className="flex gap-2">
                        <Input
                          id="custom-color"
                          type="color"
                          value={selectedColor}
                          onChange={(e) => handleColorChange(e.target.value)}
                          className="w-16 h-10 p-1 border rounded"
                        />
                        <Input
                          type="text"
                          value={selectedColor}
                          onChange={(e) => handleColorChange(e.target.value)}
                          placeholder="#dc2626"
                          className="flex-1"
                        />
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg bg-gray-50">
                      <h5 className="font-medium mb-2">معاينة الألوان</h5>
                      <div className="flex gap-4">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-6 h-6 rounded-full"
                            style={{ backgroundColor: selectedColor }}
                          ></div>
                          <span className="text-sm">اللون الأساسي</span>
                        </div>
                        <Button
                          size="sm"
                          style={{ backgroundColor: selectedColor, borderColor: selectedColor }}
                        >
                          زر تجريبي
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Logo Upload */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">شعار الشركة</h4>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <div className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <span className="text-white font-bold text-xl">YG</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      يمكن تحميل ملف PNG أو SVG بحجم أقصى 64 ميجابايت
                    </p>
                    <Button variant="outline" className="gap-2">
                      <Upload className="w-4 h-4" />
                      تحميل شعار جديد
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="language" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات اللغة</CardTitle>
                <CardDescription>تخصيص لغة النظام والواجهة</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">اللغة الأساسية</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="language">اللغة الافتراضية</Label>
                      <Select defaultValue="ar">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ar">العربية</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timezone-lang">المنطقة الزمنية</Label>
                      <Select defaultValue="riyadh">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="riyadh">الرياض (GMT+3)</SelectItem>
                          <SelectItem value="dubai">دبي (GMT+4)</SelectItem>
                          <SelectItem value="kuwait">الكويت (GMT+3)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-medium">الميزات المتقدمة</h4>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>تفعيل الترجمة التلقائية</Label>
                        <p className="text-sm text-gray-500">تفعيل الترجمة التلقائية بين العربية والإنجليزية</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>تحويل التاريخ التلقائي</Label>
                        <p className="text-sm text-gray-500">تحويل التواريخ حسب التقويم المحلي والهجري</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ai" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الذكاء الاصطناعي</CardTitle>
                <CardDescription>تحكم في ميزات الذكاء الاصطناعي والنماذج المخصصة</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* AI Toggle */}
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>تفعيل الذكاء الاصطناعي</Label>
                    <p className="text-sm text-gray-500">استخدام نماذج الذكاء الاصطناعي لتحليل الملفات وتقديم التوصيات</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                {/* AI Models */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">النماذج المخصصة</h4>

                  <div className="flex items-center justify-between">
                    <Dialog open={isAddModelDialogOpen} onOpenChange={setIsAddModelDialogOpen}>
                      <DialogTrigger asChild>
                        <Button className="bg-red-600 hover:bg-red-700 gap-2">
                          <Plus className="w-4 h-4" />
                          إضافة نموذج
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>إضافة نموذج ذكاء اصطناعي جديد</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="model-name">اسم النموذج</Label>
                            <Input
                              id="model-name"
                              placeholder="مثال: GPT-4 Custom"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="model-provider">المزود</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر المزود" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="openai">OpenAI</SelectItem>
                                <SelectItem value="anthropic">Anthropic</SelectItem>
                                <SelectItem value="google">Google</SelectItem>
                                <SelectItem value="custom">مخصص</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="api-key">مفتاح API</Label>
                            <Input
                              id="api-key"
                              type="password"
                              placeholder="أدخل مفتاح API"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="cost-per-token">التكلفة لكل 1000 رمز (ريال)</Label>
                            <Input
                              id="cost-per-token"
                              type="number"
                              step="0.01"
                              placeholder="0.50"
                            />
                          </div>

                          <div className="flex gap-2 pt-4">
                            <Button
                              className="bg-red-600 hover:bg-red-700 flex-1"
                              onClick={() => setIsAddModelDialogOpen(false)}
                            >
                              إضافة النموذج
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => setIsAddModelDialogOpen(false)}
                            >
                              إلغاء
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {/* GPT-4 Turbo */}
                  <Card className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <Bot className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h5 className="font-medium">GPT-4 Turbo</h5>
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">نشط</span>
                          </div>
                          <p className="text-sm text-gray-500">أحدث نموذج OpenAI</p>
                          <p className="text-xs text-gray-400">0.7 ريال لكل 1000 رمز • 4096 رمز كحد أقصى</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch defaultChecked />
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-md">
                            <DialogHeader>
                              <DialogTitle>تحرير نموذج GPT-4 Turbo</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <Label>اسم النموذج</Label>
                                <Input defaultValue="GPT-4 Turbo" />
                              </div>
                              <div className="space-y-2">
                                <Label>مفتاح API</Label>
                                <Input type="password" defaultValue="sk-..." />
                              </div>
                              <div className="space-y-2">
                                <Label>التكلفة لكل 1000 رمز</Label>
                                <Input type="number" defaultValue="0.7" step="0.01" />
                              </div>
                              <div className="flex gap-2 pt-4">
                                <DialogTrigger asChild>
                                  <Button className="bg-red-600 hover:bg-red-700 flex-1">
                                    حفظ التغييرات
                                  </Button>
                                </DialogTrigger>
                                <DialogTrigger asChild>
                                  <Button variant="outline">إلغاء</Button>
                                </DialogTrigger>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>

                  {/* Claude 3 Sonnet */}
                  <Card className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Bot className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h5 className="font-medium">Claude 3 Sonnet</h5>
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">غير نشط</span>
                          </div>
                          <p className="text-sm text-gray-500">نموذج Claude من Anthropic</p>
                          <p className="text-xs text-gray-400">0.5 ريال لكل 1000 رمز • 4096 رمز كحد أقصى</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch />
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-md">
                            <DialogHeader>
                              <DialogTitle>تحرير نموذج Claude 3 Sonnet</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <Label>اسم النموذج</Label>
                                <Input defaultValue="Claude 3 Sonnet" />
                              </div>
                              <div className="space-y-2">
                                <Label>مفتاح API</Label>
                                <Input type="password" defaultValue="sk-ant-..." />
                              </div>
                              <div className="space-y-2">
                                <Label>التكلفة لكل 1000 رمز</Label>
                                <Input type="number" defaultValue="0.5" step="0.01" />
                              </div>
                              <div className="flex gap-2 pt-4">
                                <DialogTrigger asChild>
                                  <Button className="bg-red-600 hover:bg-red-700 flex-1">
                                    حفظ التغييرات
                                  </Button>
                                </DialogTrigger>
                                <DialogTrigger asChild>
                                  <Button variant="outline">إلغاء</Button>
                                </DialogTrigger>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                </div>



                {/* Default Model */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">النموذج الافتراضي</h4>

                  <Select defaultValue="gpt-4-turbo">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gpt-4-turbo">GPT-4 (يُنصح بشدة)</SelectItem>
                      <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                      <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Features */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">الميزات المتقدمة</h4>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>تحليل السير الذاتية</Label>
                        <p className="text-sm text-gray-500">تحليل تلقائي للسير الذاتية واستخراج المهارات</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>مطابقة الوظائف</Label>
                        <p className="text-sm text-gray-500">اقتراح أفضل المرشحين للوظائف المتاحة</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>الاستجابة الذكية</Label>
                        <p className="text-sm text-gray-500">تقديم ردود ذكية لاستفسارات المرشحين والعملاء</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>التدريب الذكي</Label>
                        <p className="text-sm text-gray-500">تحسين النماذج على أساس البيانات المحلية</p>
                      </div>
                      <Switch />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الإيرادات</CardTitle>
                <CardDescription>تكوين آليات حساب الإيرادات والعمولات حسب الشركة</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Currency Settings */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">إعدادات العملة</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="default-currency">العملة الافتراضية</Label>
                      <Select value={defaultCurrency} onValueChange={setDefaultCurrency}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="SAR">ريال سعودي (SAR)</SelectItem>
                          <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                          <SelectItem value="EUR">يورو (EUR)</SelectItem>
                          <SelectItem value="AED">درهم إماراتي (AED)</SelectItem>
                          <SelectItem value="KWD">دينار كويتي (KWD)</SelectItem>
                          <SelectItem value="QAR">ريال قطري (QAR)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>تحويل العملة التلقائي</Label>
                        <p className="text-sm text-gray-500">تحويل تلقائي للعملات حسب أسعار الصرف</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>

                {/* Company-specific Commission */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-lg font-medium">عمولات مخصصة للشركات</h4>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button className="bg-red-600 hover:bg-red-700 gap-2">
                          <Plus className="w-4 h-4" />
                          إضافة آلية جديدة
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>إضافة آلية عمولة جديدة</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="commission-name">اسم الآلية</Label>
                            <Input id="commission-name" placeholder="مثال: عمولة أرامكو" />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="commission-type">نوع العمولة</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر نوع العمولة" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="percentage">نسبة مئوية</SelectItem>
                                <SelectItem value="fixed">مبلغ ثابت</SelectItem>
                                <SelectItem value="package">باقة شاملة</SelectItem>
                                <SelectItem value="consulting">استشارات</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>الشركات المطبقة</Label>
                            <div className="space-y-2">
                              <p className="text-sm text-gray-500">اختر الشركات التي تنطبق عليها هذه الآلية:</p>
                              <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto border rounded p-2">
                                {[
                                  { id: "aramco", name: "أرامكو السعودية", active: true },
                                  { id: "sabic", name: "سابك", active: true },
                                  { id: "alahli", name: "البنك الأهلي السعودي", active: true },
                                  { id: "stc", name: "الاتصالات السعودية", active: true },
                                  { id: "rajhi", name: "بنك الراجحي", active: true },
                                  { id: "maaden", name: "معادن", active: false },
                                  { id: "acwa", name: "أكوا باور", active: false },
                                  { id: "neom", name: "نيوم", active: false }
                                ].map((company) => (
                                  <label key={company.id} className="flex items-center space-x-2 space-x-reverse p-2 hover:bg-gray-50 rounded">
                                    <input
                                      type="checkbox"
                                      defaultChecked={company.active}
                                      className="rounded border-gray-300"
                                    />
                                    <span className="text-sm">{company.name}</span>
                                    <div className={`w-2 h-2 rounded-full ml-auto ${company.active ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                                  </label>
                                ))}
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="commission-value">القيمة</Label>
                            <div className="flex gap-2">
                              <Input id="commission-value" type="number" placeholder="0" className="flex-1" />
                              <Select defaultValue="SAR">
                                <SelectTrigger className="w-24">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="SAR">ريال</SelectItem>
                                  <SelectItem value="USD">دولار</SelectItem>
                                  <SelectItem value="percent">%</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div className="flex gap-2 pt-4">
                            <DialogTrigger asChild>
                              <Button className="bg-red-600 hover:bg-red-700 flex-1">
                                إضافة الآلية
                              </Button>
                            </DialogTrigger>
                            <DialogTrigger asChild>
                              <Button variant="outline">إلغاء</Button>
                            </DialogTrigger>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {/* Existing Commission Rules */}
                  <div className="space-y-4">
                    <Card className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h5 className="font-medium">عمولة أرامكو - نسبة مئوية</h5>
                          <p className="text-sm text-gray-500">3% من الراتب لمدة 6 أشهر</p>
                          <div className="flex gap-2 mt-1">
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">أرامكو السعودية</span>
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">نشط</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch defaultChecked />
                          <Button variant="outline" size="sm">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">النسبة</p>
                          <p className="font-medium">3%</p>
                        </div>
                        <div>
                          <p className="text-gray-500">المدة</p>
                          <p className="font-medium">6 أشهر</p>
                        </div>
                        <div>
                          <p className="text-gray-500">الحد الأدنى</p>
                          <p className="font-medium">5,000 ر.س</p>
                        </div>
                      </div>
                    </Card>

                    <Card className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h5 className="font-medium">عمولة البنوك - مبلغ ثابت</h5>
                          <p className="text-sm text-gray-500">مبلغ ثابت حسب مستوى الوظيفة</p>
                          <div className="flex gap-2 mt-1">
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">البنك الأهلي</span>
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">بنك الراجحي</span>
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">نشط</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch defaultChecked />
                          <Button variant="outline" size="sm">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">المبلغ</p>
                          <p className="font-medium">8,000 ر.س</p>
                        </div>
                        <div>
                          <p className="text-gray-500">المستوى</p>
                          <p className="font-medium">متوسط</p>
                        </div>
                        <div>
                          <p className="text-gray-500">العملة</p>
                          <p className="font-medium">ريال سعودي</p>
                        </div>
                      </div>
                    </Card>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-medium">آليات حساب العمولات العامة</h4>

                  {/* Percentage-based commission */}
                  <Card className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h5 className="font-medium">عمولة نسبة مئوية من الراتب</h5>
                        <p className="text-sm text-gray-500">نسبة مئوية من راتب الموظف لفترة محددة</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="percentage">النسبة المئوية (%)</Label>
                        <Input
                          id="percentage"
                          type="number"
                          min="0"
                          max="100"
                          defaultValue="3"
                          placeholder="3"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="duration">المدة (بالأشهر)</Label>
                        <Input
                          id="duration"
                          type="number"
                          min="1"
                          max="24"
                          defaultValue="6"
                          placeholder="6"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="min-salary">الحد الأدنى للراتب</Label>
                        <Input
                          id="min-salary"
                          type="number"
                          min="0"
                          defaultValue="5000"
                          placeholder="5000"
                        />
                      </div>
                    </div>
                  </Card>

                  {/* Fixed amount commission */}
                  <Card className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h5 className="font-medium">عمولة مبلغ ثابت</h5>
                        <p className="text-sm text-gray-500">مبلغ ثابت لكل عملية توظيف</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="fixed-amount">المبلغ الثابت (ريال)</Label>
                        <Input
                          id="fixed-amount"
                          type="number"
                          min="0"
                          defaultValue="5000"
                          placeholder="5000"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="job-level">مستوى الوظيفة</Label>
                        <Select defaultValue="all">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">جميع المستويات</SelectItem>
                            <SelectItem value="entry">مبتدئ</SelectItem>
                            <SelectItem value="mid">متوسط</SelectItem>
                            <SelectItem value="senior">خبير</SelectItem>
                            <SelectItem value="executive">تنفيذي</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </Card>

                  {/* Package deal commission */}
                  <Card className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h5 className="font-medium">عقود الباقات الشاملة</h5>
                        <p className="text-sm text-gray-500">مبلغ شامل لتوفير عدد معين من الموظفين</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="package-amount">مبلغ الباقة (ريال)</Label>
                        <Input
                          id="package-amount"
                          type="number"
                          min="0"
                          defaultValue="50000"
                          placeholder="50000"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="employee-count">عدد الموظفين</Label>
                        <Input
                          id="employee-count"
                          type="number"
                          min="1"
                          defaultValue="10"
                          placeholder="10"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="package-duration">مدة التنفيذ (أشهر)</Label>
                        <Input
                          id="package-duration"
                          type="number"
                          min="1"
                          defaultValue="12"
                          placeholder="12"
                        />
                      </div>
                    </div>
                  </Card>

                  {/* Consulting services */}
                  <Card className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h5 className="font-medium">الخدمات الاستشارية</h5>
                        <p className="text-sm text-gray-500">أسعار الخدمات الاستشارية والتدريب</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="hourly-rate">السعر بالساعة (ريال)</Label>
                        <Input
                          id="hourly-rate"
                          type="number"
                          min="0"
                          defaultValue="500"
                          placeholder="500"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="daily-rate">السعر اليومي (ريال)</Label>
                        <Input
                          id="daily-rate"
                          type="number"
                          min="0"
                          defaultValue="3000"
                          placeholder="3000"
                        />
                      </div>
                    </div>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="integrations" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card className="text-center p-6">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                  <Settings className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">عام</h3>
                <p className="text-sm text-gray-500">إعدادات التطبيقات</p>
                <p className="text-2xl font-bold text-blue-600 mt-2">5</p>
                <p className="text-xs text-gray-400">إجمالي التطبيقات</p>
              </Card>

              <Card className="text-center p-6">
                <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
                  <Zap className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">نشطة</h3>
                <p className="text-sm text-gray-500">متصلة</p>
                <p className="text-2xl font-bold text-green-600 mt-2">2</p>
                <p className="text-xs text-gray-400">مفعلة</p>
              </Card>

              <Card className="text-center p-6">
                <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg mx-auto mb-4">
                  <Settings className="w-6 h-6 text-gray-600" />
                </div>
                <h3 className="font-semibold mb-2">غير نشطة</h3>
                <p className="text-sm text-gray-500">الذكاء الاصطناعي</p>
                <p className="text-2xl font-bold text-gray-600 mt-2">2</p>
                <p className="text-xs text-gray-400">معطلة</p>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>التكاملات المتاحة</CardTitle>
                    <CardDescription>قم بربط النظام مع الخدمات الخارجية لتحسين الأداء</CardDescription>
                  </div>
                  <Dialog open={isAddIntegrationDialogOpen} onOpenChange={setIsAddIntegrationDialogOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-red-600 hover:bg-red-700 gap-2">
                        <Plus className="w-4 h-4" />
                        إضافة تكامل
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>إضافة تكامل مخصص</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="integration-name">اسم التكامل</Label>
                          <Input
                            id="integration-name"
                            placeholder="مثال: Slack API"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="integration-type">نوع التكامل</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر النوع" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="api">API</SelectItem>
                              <SelectItem value="webhook">Webhook</SelectItem>
                              <SelectItem value="oauth">OAuth</SelectItem>
                              <SelectItem value="custom">مخصص</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="api-endpoint">API Endpoint</Label>
                          <Input
                            id="api-endpoint"
                            placeholder="https://api.example.com"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="api-key">مفتاح API</Label>
                          <Input
                            id="api-key"
                            type="password"
                            placeholder="أدخل مفتاح API"
                          />
                        </div>

                        <div className="flex gap-2 pt-4">
                          <Button
                            className="bg-red-600 hover:bg-red-700 flex-1"
                            onClick={() => setIsAddIntegrationDialogOpen(false)}
                          >
                            إضافة التكامل
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setIsAddIntegrationDialogOpen(false)}
                          >
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Workable API */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Globe className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Workable API</h4>
                      <p className="text-sm text-gray-500">استيراد الوظائف والمرشحين من Workable</p>
                      <div className="flex gap-2 mt-1">
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">سيرة ذاتية</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">مرشح جديد</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">تحديث حالات</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm text-red-600">فشل الاتصال</span>
                    </div>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>إعداد Workable API</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="workable-api-key">مفتاح API</Label>
                            <Input
                              id="workable-api-key"
                              placeholder="أدخل مفتاح API الخاص بك"
                              type="password"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="workable-subdomain">Subdomain</Label>
                            <Input
                              id="workable-subdomain"
                              placeholder="company.workable.com"
                            />
                          </div>

                          <div className="flex gap-2 pt-4">
                            <DialogTrigger asChild>
                              <Button className="bg-red-600 hover:bg-red-700 flex-1">
                                حفظ الإعدادات
                              </Button>
                            </DialogTrigger>
                            <DialogTrigger asChild>
                              <Button variant="outline">إلغاء</Button>
                            </DialogTrigger>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                {/* LinkedIn API */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Link className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">LinkedIn API</h4>
                      <p className="text-sm text-gray-500">البحث عن المرشحين ونشر الوظائف على LinkedIn</p>
                      <div className="flex gap-2 mt-1">
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">نشر وظائف</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">البحث عن مرشحين</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">استيراد الملفات الشخصية</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-green-600">متصل</span>
                    </div>
                    <Switch defaultChecked />
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>إعداد LinkedIn API</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="linkedin-client-id">Client ID</Label>
                            <Input
                              id="linkedin-client-id"
                              placeholder="أدخل Client ID"
                              type="password"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="linkedin-client-secret">Client Secret</Label>
                            <Input
                              id="linkedin-client-secret"
                              placeholder="أدخل Client Secret"
                              type="password"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>الصلاحيات المطلوبة</Label>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm">نشر الوظائف</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm">البحث عن المرشحين</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm">استيراد الملفات الشخصية</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex gap-2 pt-4">
                            <DialogTrigger asChild>
                              <Button className="bg-red-600 hover:bg-red-700 flex-1">
                                حفظ الإعدادات
                              </Button>
                            </DialogTrigger>
                            <DialogTrigger asChild>
                              <Button variant="outline">إلغاء</Button>
                            </DialogTrigger>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="outline" size="sm">
                      <RefreshCw className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* WhatsApp Cloud API */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <MessageSquare className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">WhatsApp Cloud API</h4>
                      <p className="text-sm text-gray-500">إرسال الإشعارات والرسائل عبر WhatsApp</p>
                      <div className="flex gap-2 mt-1">
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">إشعارات مقابلات</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">متابعة حالة</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">رسائل ترحيبية</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch defaultChecked />
                    <Dialog open={isWhatsAppDialogOpen} onOpenChange={setIsWhatsAppDialogOpen}>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>إعداد WhatsApp Cloud API</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="whatsapp-api-key">مفتاح API</Label>
                            <Input
                              id="whatsapp-api-key"
                              placeholder="أدخل مفتاح API الخاص بك"
                              type="password"
                            />
                            <p className="text-xs text-gray-500">
                              يمكن الحصول على مفتاح API من WhatsApp Cloud API
                            </p>
                          </div>

                          <div className="space-y-2">
                            <Label>الميزات المتاحة</Label>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm">إشعارات مقابلات</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm">متابعة حالة</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm">رسائل ترحيبية</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex gap-2 pt-4">
                            <Button
                              className="bg-red-600 hover:bg-red-700 flex-1"
                              onClick={() => setIsWhatsAppDialogOpen(false)}
                            >
                              حفظ الإعدادات
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => setIsWhatsAppDialogOpen(false)}
                            >
                              إلغاء
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="outline" size="sm">
                      <RefreshCw className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Google Calendar */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                      <Calendar className="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Google Calendar</h4>
                      <p className="text-sm text-gray-500">جدولة المقابلات ومزامنة التقويم</p>
                      <div className="flex gap-2 mt-1">
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">جدولة مقابلات</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">تقويم مشترك</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">مزامنة تلقائية</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">معطل</span>
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Zapier */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Zap className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">Zapier</h4>
                      <p className="text-sm text-gray-500">ربط النظام مع أكثر من 3000 تطبيق</p>
                      <div className="flex gap-2 mt-1">
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">أتمتة مهام</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">ربط تطبيقات</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">مزامعة البيانات</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">غير متصل</span>
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>


        </Tabs>

        <div className="flex justify-end space-x-2 space-x-reverse">
          <Button variant="outline" onClick={handleResetSettings}>
            <RefreshCw className="w-4 h-4 ml-2" />
            إعادة تعيين
          </Button>
          <Button onClick={handleSaveSettings} disabled={isLoading}>
            <Save className="w-4 h-4 ml-2" />
            {isLoading ? "جاري الحفظ..." : "حفظ الإعدادات"}
          </Button>
        </div>
      </div>
    </DashboardLayout>
  )
}
