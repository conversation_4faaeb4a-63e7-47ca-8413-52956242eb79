"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Building2, DollarSign, Users, Eye } from "lucide-react"

interface Job {
  id: string
  title: string
  company: string
  location: string
  type: "full-time" | "part-time" | "contract" | "remote"
  level: "entry" | "mid" | "senior" | "executive"
  salary: {
    min: number
    max: number
    currency: string
  }
  lat: number
  lng: number
  applicants: number
}

interface JobMapProps {
  jobs: Job[]
  onJobSelect: (job: Job) => void
  selectedJob?: Job | null
}

export function JobMap({ jobs, onJobSelect, selectedJob }: JobMapProps) {
  const [mapCenter, setMapCenter] = useState({ lat: 24.7136, lng: 46.6753 }) // الرياض
  const [zoom, setZoom] = useState(7)
  const [selectedCity, setSelectedCity] = useState<string | null>(null)

  // محاكاة خريطة تفاعلية
  const cities = [
    { name: "الرياض", lat: 24.7136, lng: 46.6753 },
    { name: "جدة", lat: 21.4858, lng: 39.1925 },
    { name: "الدمام", lat: 26.4207, lng: 50.0888 },
    { name: "مكة", lat: 21.3891, lng: 39.8579 },
    { name: "المدينة", lat: 24.5247, lng: 39.5692 }
  ]

  // تجميع الوظائف حسب المدينة
  const getJobsByCity = (cityName: string) => {
    return jobs.filter(job => job.location.includes(cityName))
  }

  const getTypeColor = (type: Job["type"]) => {
    switch (type) {
      case "full-time": return "bg-blue-500"
      case "part-time": return "bg-purple-500"
      case "contract": return "bg-orange-500"
      case "remote": return "bg-green-500"
      default: return "bg-gray-500"
    }
  }

  const getTypeName = (type: Job["type"]) => {
    switch (type) {
      case "full-time": return "دوام كامل"
      case "part-time": return "دوام جزئي"
      case "contract": return "عقد"
      case "remote": return "عن بُعد"
      default: return type
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* الخريطة */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              خريطة الوظائف
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative bg-gradient-to-br from-blue-50 to-green-50 rounded-lg h-96 overflow-hidden border">
              {/* محاكاة خريطة السعودية */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
                <svg viewBox="0 0 400 300" className="w-full h-full">
                  {/* حدود السعودية المبسطة */}
                  <path
                    d="M50 150 L100 100 L200 80 L300 90 L350 120 L370 180 L340 220 L280 240 L200 250 L120 240 L80 200 Z"
                    fill="rgba(34, 197, 94, 0.1)"
                    stroke="rgba(34, 197, 94, 0.3)"
                    strokeWidth="2"
                  />

                  {/* المدن */}
                  {cities.map((city, index) => {
                    const x = (city.lng - 34) * 8
                    const y = (32 - city.lat) * 8
                    const cityJobs = getJobsByCity(city.name)
                    const isSelected = selectedCity === city.name

                    return (
                      <g key={city.name}>
                        <circle
                          cx={x}
                          cy={y}
                          r={Math.max(12, Math.min(cityJobs.length * 3, 25))}
                          fill={isSelected ? "rgba(59, 130, 246, 0.8)" : "rgba(239, 68, 68, 0.7)"}
                          stroke="white"
                          strokeWidth="3"
                          className="cursor-pointer hover:opacity-80 transition-all duration-200"
                          onClick={() => {
                            setMapCenter({ lat: city.lat, lng: city.lng })
                            setSelectedCity(selectedCity === city.name ? null : city.name)
                          }}
                        />
                        <text
                          x={x}
                          y={y - 20}
                          textAnchor="middle"
                          className="text-sm font-bold fill-gray-800 pointer-events-none"
                        >
                          {city.name}
                        </text>
                        <text
                          x={x}
                          y={y + 30}
                          textAnchor="middle"
                          className="text-xs font-medium fill-gray-700 pointer-events-none"
                        >
                          {cityJobs.length} وظيفة
                        </text>

                        {/* عرض الوظائف عند التحديد */}
                        {isSelected && cityJobs.length > 0 && (
                          <g>
                            {cityJobs.slice(0, 3).map((job, jobIndex) => (
                              <g key={job.id}>
                                <circle
                                  cx={x + (jobIndex - 1) * 15}
                                  cy={y + 50}
                                  r="6"
                                  fill={getTypeColor(job.type)}
                                  stroke="white"
                                  strokeWidth="2"
                                  className="cursor-pointer hover:r-8 transition-all"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onJobSelect(job)
                                  }}
                                />
                              </g>
                            ))}
                          </g>
                        )}
                      </g>
                    )
                  })}
                </svg>
              </div>

              {/* أدوات التحكم */}
              <div className="absolute top-4 left-4 flex flex-col gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-white/90"
                  onClick={() => setZoom(Math.min(zoom + 1, 10))}
                >
                  +
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-white/90"
                  onClick={() => setZoom(Math.max(zoom - 1, 1))}
                >
                  -
                </Button>
              </div>

              {/* مفتاح الخريطة */}
              <div className="absolute bottom-4 right-4 bg-white/95 p-3 rounded-lg shadow-lg border">
                <h4 className="text-sm font-medium mb-2">مفتاح الخريطة</h4>
                <div className="space-y-2">
                  <div className="text-xs text-gray-600 mb-1">أنواع الوظائف:</div>
                  {["full-time", "part-time", "contract", "remote"].map((type) => (
                    <div key={type} className="flex items-center gap-2 text-xs">
                      <div className={`w-3 h-3 rounded-full ${getTypeColor(type as Job["type"])}`}></div>
                      <span>{getTypeName(type as Job["type"])}</span>
                    </div>
                  ))}
                  <div className="border-t pt-2 mt-2">
                    <div className="text-xs text-gray-600 mb-1">التفاعل:</div>
                    <div className="text-xs text-gray-500">
                      • انقر على المدينة لعرض الوظائف
                      <br />
                      • انقر على النقاط الصغيرة لعرض تفاصيل الوظيفة
                    </div>
                  </div>
                </div>
              </div>

              {/* معلومات المدينة المحددة */}
              {selectedCity && (
                <div className="absolute top-4 right-4 bg-white/95 p-3 rounded-lg shadow-lg border max-w-xs">
                  <h4 className="font-medium text-sm mb-2">{selectedCity}</h4>
                  <div className="text-xs text-gray-600">
                    {getJobsByCity(selectedCity).length} وظيفة متاحة
                  </div>
                  <div className="mt-2 space-y-1">
                    {getJobsByCity(selectedCity).slice(0, 3).map((job) => (
                      <div
                        key={job.id}
                        className="text-xs p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
                        onClick={() => onJobSelect(job)}
                      >
                        <div className="font-medium">{job.title}</div>
                        <div className="text-gray-500">{job.company}</div>
                      </div>
                    ))}
                    {getJobsByCity(selectedCity).length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{getJobsByCity(selectedCity).length - 3} وظيفة أخرى
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* قائمة الوظائف */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle>الوظائف المتاحة ({jobs.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="max-h-96 overflow-y-auto">
              {jobs.map((job) => (
                <div
                  key={job.id}
                  className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                    selectedJob?.id === job.id ? "bg-blue-50 border-blue-200" : ""
                  }`}
                  onClick={() => onJobSelect(job)}
                >
                  <div className="space-y-2">
                    <div>
                      <h4 className="font-medium text-sm">{job.title}</h4>
                      <div className="flex items-center gap-1 text-xs text-gray-600">
                        <Building2 className="h-3 w-3" />
                        {job.company}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <MapPin className="h-3 w-3" />
                      {job.location}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Badge className={`text-xs ${getTypeColor(job.type)} text-white`}>
                        {getTypeName(job.type)}
                      </Badge>
                      <div className="flex items-center gap-1 text-xs text-green-600">
                        <DollarSign className="h-3 w-3" />
                        {job.salary.min.toLocaleString()}-{job.salary.max.toLocaleString()}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {job.applicants} متقدم
                      </div>
                      <Button size="sm" variant="ghost" className="h-6 px-2 text-xs">
                        <Eye className="h-3 w-3 mr-1" />
                        عرض
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
