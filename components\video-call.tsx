"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@/components/ui/dialog"
import { Video, VideoOff, Mic, MicOff, PhoneOff, Monitor, Users, MessageSquare, Settings } from "lucide-react"

interface VideoCallProps {
  isOpen: boolean
  onClose: () => void
  participants: {
    id: string
    name: string
    role: string
    avatar?: string
  }[]
  meetingTitle: string
}

export function VideoCall({ isOpen, onClose, participants, meetingTitle }: VideoCallProps) {
  const [isVideoOn, setIsVideoOn] = useState(true)
  const [isAudioOn, setIsAudioOn] = useState(true)
  const [isScreenSharing, setIsScreenSharing] = useState(false)
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [callDuration, setCallDuration] = useState(0)
  const [callStatus, setCallStatus] = useState<"connecting" | "connected" | "ended">("connecting")

  const localVideoRef = useRef<HTMLVideoElement>(null)
  const remoteVideoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (isOpen) {
      setCallStatus("connecting")
      // Simulate connection
      const timer = setTimeout(() => {
        setCallStatus("connected")
      }, 2000)

      return () => clearTimeout(timer)
    }
  }, [isOpen])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (callStatus === "connected") {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [callStatus])

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleEndCall = () => {
    setCallStatus("ended")
    setCallDuration(0)
    onClose()
  }

  const toggleVideo = () => {
    setIsVideoOn(!isVideoOn)
  }

  const toggleAudio = () => {
    setIsAudioOn(!isAudioOn)
  }

  const toggleScreenShare = () => {
    setIsScreenSharing(!isScreenSharing)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <div>
              <DialogTitle className="text-lg">{meetingTitle}</DialogTitle>
              <DialogDescription className="flex items-center gap-2">
                <Badge variant={callStatus === "connected" ? "default" : "secondary"}>
                  {callStatus === "connecting" && "جاري الاتصال..."}
                  {callStatus === "connected" && `متصل - ${formatDuration(callDuration)}`}
                  {callStatus === "ended" && "انتهت المكالمة"}
                </Badge>
                <span className="text-sm text-gray-500">
                  <Users className="h-4 w-4 inline mr-1" />
                  {participants.length} مشارك
                </span>
              </DialogDescription>
            </div>
            <Button variant="outline" size="sm" onClick={() => setIsChatOpen(!isChatOpen)}>
              <MessageSquare className="h-4 w-4 mr-2" />
              الدردشة
            </Button>
          </div>

          {/* Video Area */}
          <div className="flex-1 flex">
            <div className="flex-1 relative bg-gray-900">
              {callStatus === "connecting" ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <p>جاري الاتصال...</p>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-2 h-full p-2">
                  {/* Local Video */}
                  <div className="relative bg-gray-800 rounded-lg overflow-hidden">
                    {isVideoOn ? (
                      <div className="w-full h-full bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
                        <div className="text-white text-center">
                          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span className="text-2xl">👤</span>
                          </div>
                          <p className="text-sm">أنت</p>
                        </div>
                      </div>
                    ) : (
                      <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                        <div className="text-center text-gray-300">
                          <VideoOff className="h-12 w-12 mx-auto mb-2" />
                          <p>الكاميرا مغلقة</p>
                        </div>
                      </div>
                    )}
                    <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                      أنت {!isAudioOn && <MicOff className="h-3 w-3 inline ml-1" />}
                    </div>
                  </div>

                  {/* Remote Video */}
                  {participants.map((participant, index) => (
                    <div key={participant.id} className="relative bg-gray-800 rounded-lg overflow-hidden">
                      <div className="w-full h-full bg-gradient-to-br from-green-600 to-blue-600 flex items-center justify-center">
                        <div className="text-white text-center">
                          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span className="text-2xl">👤</span>
                          </div>
                          <p className="text-sm">{participant.name}</p>
                          <p className="text-xs opacity-75">{participant.role}</p>
                        </div>
                      </div>
                      <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                        {participant.name}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Screen Share Indicator */}
              {isScreenSharing && (
                <div className="absolute top-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  تتم مشاركة الشاشة
                </div>
              )}
            </div>

            {/* Chat Panel */}
            {isChatOpen && (
              <div className="w-80 border-l bg-gray-50 dark:bg-gray-900 flex flex-col">
                <div className="p-3 border-b">
                  <h3 className="font-medium">الدردشة</h3>
                </div>
                <div className="flex-1 p-3 space-y-3 overflow-y-auto">
                  <div className="bg-white dark:bg-gray-800 p-2 rounded-lg">
                    <p className="text-sm font-medium">أحمد محمد</p>
                    <p className="text-xs text-gray-600 dark:text-gray-300">مرحباً بالجميع</p>
                    <span className="text-xs text-gray-400">10:30</span>
                  </div>
                  <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
                    <p className="text-sm font-medium">أنت</p>
                    <p className="text-xs text-gray-600 dark:text-gray-300">أهلاً وسهلاً</p>
                    <span className="text-xs text-gray-400">10:31</span>
                  </div>
                </div>
                <div className="p-3 border-t">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="اكتب رسالة..."
                      className="flex-1 px-3 py-2 border rounded-lg text-sm"
                    />
                    <Button size="sm">إرسال</Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="p-4 border-t bg-gray-50 dark:bg-gray-900">
            <div className="flex items-center justify-center gap-4">
              <Button
                variant={isAudioOn ? "default" : "destructive"}
                size="sm"
                onClick={toggleAudio}
                className="rounded-full w-12 h-12"
              >
                {isAudioOn ? <Mic className="h-5 w-5" /> : <MicOff className="h-5 w-5" />}
              </Button>

              <Button
                variant={isVideoOn ? "default" : "destructive"}
                size="sm"
                onClick={toggleVideo}
                className="rounded-full w-12 h-12"
              >
                {isVideoOn ? <Video className="h-5 w-5" /> : <VideoOff className="h-5 w-5" />}
              </Button>

              <Button
                variant={isScreenSharing ? "default" : "outline"}
                size="sm"
                onClick={toggleScreenShare}
                className="rounded-full w-12 h-12"
              >
                <Monitor className="h-5 w-5" />
              </Button>

              <Button variant="outline" size="sm" className="rounded-full w-12 h-12 bg-transparent">
                <Settings className="h-5 w-5" />
              </Button>

              <Button
                variant="destructive"
                size="sm"
                onClick={handleEndCall}
                className="rounded-full w-12 h-12 bg-red-600 hover:bg-red-700"
              >
                <PhoneOff className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
