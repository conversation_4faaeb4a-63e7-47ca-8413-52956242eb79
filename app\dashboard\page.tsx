"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Users, Briefcase, TrendingUp, DollarSign, Calendar, FileText, Target, Clock, ArrowUpRight } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { AIChatbot } from "@/components/ai-chatbot"
import Link from "next/link"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/hooks/use-auth"

export default function DashboardPage() {
  const [selectedCard, setSelectedCard] = useState<string | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()

  // إعادة توجيه الباحثين عن عمل إلى صفحة الوظائف المتاحة
  useEffect(() => {
    if (isAuthenticated && user?.role === 'job_seeker') {
      router.push('/dashboard/available-jobs')
      return
    }
  }, [user, isAuthenticated, router])

  // عدم عرض أي شيء للباحثين عن عمل أثناء إعادة التوجيه
  if (user?.role === 'job_seeker') {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري التحميل...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const stats = [
    {
      id: "clients",
      title: "إجمالي العملاء",
      value: "127",
      change: "+12",
      icon: Users,
      color: "text-blue-600",
      href: "/dashboard/clients",
      details: {
        active: 115,
        inactive: 12,
        newThisMonth: 12,
        topClients: ["شركة أرامكو السعودية", "البنك الأهلي السعودي", "شركة سابك", "شركة الاتصالات السعودية", "مجموعة بن لادن"],
      },
    },
    {
      id: "jobs",
      title: "الوظائف النشطة",
      value: "89",
      change: "+15",
      icon: Briefcase,
      color: "text-green-600",
      href: "/dashboard/jobs",
      details: {
        fullTime: 67,
        partTime: 14,
        contract: 8,
        newThisWeek: 15,
        topCategories: ["تقنية المعلومات", "الهندسة", "المحاسبة والمالية", "الموارد البشرية", "التسويق والمبيعات"],
      },
    },
    {
      id: "candidates",
      title: "المرشحين الجدد",
      value: "2,847",
      change: "+342",
      icon: TrendingUp,
      color: "text-purple-600",
      href: "/dashboard/candidates",
      details: {
        thisWeek: 342,
        thisMonth: 2847,
        interviewed: 456,
        hired: 89,
        topSkills: ["React", "Node.js", "Python", "تصميم UI/UX", "إدارة المشاريع", "تحليل البيانات"],
      },
    },
    {
      id: "revenue",
      title: "الإيرادات الشهرية",
      value: "1,247,500 ر.س",
      change: "+187,500",
      icon: DollarSign,
      color: "text-[#C0322D]",
      href: "/dashboard/reports",
      details: {
        thisMonth: 1247500,
        lastMonth: 1060000,
        target: 1300000,
        sources: [
          { name: "رسوم التوظيف", amount: 890000 },
          { name: "الاستشارات الإدارية", amount: 245000 },
          { name: "التدريب والتطوير", amount: 112500 },
        ],
      },
    },
  ]

  const recentActivities = [
    {
      id: 1,
      type: "client",
      title: "عميل جديد: شركة الراجحي المصرفية",
      time: "منذ 45 دقيقة",
      status: "جديد",
      href: "/dashboard/clients",
    },
    {
      id: 2,
      type: "job",
      title: "وظيفة جديدة: مدير تطوير الأعمال - شركة أرامكو",
      time: "منذ ساعتين",
      status: "نشط",
      href: "/dashboard/jobs",
    },
    {
      id: 3,
      type: "candidate",
      title: "مرشح جديد: أحمد محمد - مهندس DevOps",
      time: "منذ 3 ساعات",
      status: "مراجعة",
      href: "/dashboard/candidates",
    },
    {
      id: 4,
      type: "interview",
      title: "مقابلة مجدولة: فاطمة الزهراني - محاسبة قانونية",
      time: "غداً 2:00 م",
      status: "مجدولة",
      href: "/dashboard/candidates",
    },
    {
      id: 5,
      type: "placement",
      title: "تم توظيف: خالد العتيبي - مطور React Senior",
      time: "منذ 5 ساعات",
      status: "مكتمل",
      href: "/dashboard/candidates",
    },
    {
      id: 6,
      type: "client",
      title: "تجديد عقد: مستشفى الملك فيصل التخصصي",
      time: "منذ يوم واحد",
      status: "مجدد",
      href: "/dashboard/clients",
    },
  ]

  const monthlyTargets = [
    {
      id: "hiring",
      name: "التوظيف",
      current: 89,
      target: 120,
      percentage: 74,
      details: {
        completed: 89,
        inProgress: 23,
        pending: 8,
        categories: [
          { name: "تقنية المعلومات", completed: 34 },
          { name: "الهندسة", completed: 21 },
          { name: "المحاسبة والمالية", completed: 18 },
          { name: "الموارد البشرية", completed: 9 },
          { name: "التسويق والمبيعات", completed: 7 },
        ],
      },
    },
    {
      id: "clients",
      name: "العملاء الجدد",
      current: 12,
      target: 15,
      percentage: 80,
      details: {
        completed: 12,
        inProgress: 4,
        pending: 2,
        sectors: [
          { name: "التقنية والاتصالات", count: 4 },
          { name: "الصحة والطب", count: 3 },
          { name: "التعليم", count: 2 },
          { name: "البنوك والمالية", count: 2 },
          { name: "الطاقة والبتروكيماويات", count: 1 },
        ],
      },
    },
    {
      id: "interviews",
      name: "المقابلات",
      current: 456,
      target: 500,
      percentage: 91,
      details: {
        completed: 456,
        scheduled: 67,
        pending: 34,
        successRate: 82,
      },
    },
    {
      id: "revenue",
      name: "الإيرادات",
      current: 1247500,
      target: 1300000,
      percentage: 96,
      details: {
        achieved: 1247500,
        projected: 52500,
        sources: ["رسوم التوظيف", "الاستشارات الإدارية", "التدريب والتطوير"],
      },
    },
  ]

  const openCardDetails = (cardId: string) => {
    setSelectedCard(cardId)
    setIsDialogOpen(true)
  }

  const handleCardClick = (stat: any) => {
    router.push(stat.href)
  }

  const openTargetDetails = (targetId: string) => {
    setSelectedCard(targetId)
    setIsDialogOpen(true)
  }

  const getSelectedData = () => {
    const stat = stats.find((s) => s.id === selectedCard)
    const target = monthlyTargets.find((t) => t.id === selectedCard)
    return stat || target
  }

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 space-y-6 md:space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">لوحة التحكم</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">مرحباً بك، إليك نظرة عامة على أداء شركتك اليوم</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {stats.map((stat, index) => (
            <Card
              key={index}
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => handleCardClick(stat)}
            >
              <CardContent className="p-4 md:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">{stat.title}</p>
                    <p className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                    <div className="flex items-center mt-1">
                      <ArrowUpRight className="h-3 w-3 md:h-4 md:w-4 text-green-500" />
                      <span className="text-sm text-green-600 font-medium">+{stat.change}</span>
                      <span className="text-xs md:text-sm text-gray-500 mr-1">هذا الشهر</span>
                    </div>
                  </div>
                  <div className={`p-2 md:p-3 rounded-full bg-gray-100 dark:bg-gray-800 ${stat.color}`}>
                    <stat.icon className="h-5 w-5 md:h-6 md:w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid lg:grid-cols-3 gap-6 md:gap-8">
          {/* Recent Activities */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  الأنشطة الأخيرة
                </CardTitle>
                <CardDescription>آخر التحديثات والأنشطة في النظام</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <Link key={activity.id} href={activity.href}>
                      <div className="flex items-center justify-between p-3 md:p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-[#C0322D] rounded-full"></div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white text-sm md:text-base">
                              {activity.title}
                            </p>
                            <p className="text-xs md:text-sm text-gray-600 dark:text-gray-300">{activity.time}</p>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {activity.status}
                        </Badge>
                      </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Monthly Targets */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  الأهداف الشهرية
                </CardTitle>
                <CardDescription>تقدم الأهداف لهذا الشهر</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 md:space-y-6">
                  {monthlyTargets.map((target, index) => (
                    <div
                      key={index}
                      className="space-y-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-lg transition-colors"
                      onClick={() => openTargetDetails(target.id)}
                    >
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{target.name}</span>
                        <span className="text-gray-600 dark:text-gray-300">
                          {target.current.toLocaleString()}/{target.target.toLocaleString()}
                        </span>
                      </div>
                      <Progress value={target.percentage} className="h-2" />
                      <p className="text-xs text-gray-600 dark:text-gray-300">{target.percentage}% مكتمل</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>إجراءات سريعة</CardTitle>
            <CardDescription>الإجراءات الأكثر استخداماً للوصول السريع</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/dashboard/clients">
                <Button
                  variant="outline"
                  className="h-16 md:h-20 flex flex-col gap-2 bg-transparent w-full text-xs md:text-sm"
                >
                  <Users className="h-5 w-5 md:h-6 md:w-6" />
                  <span>إضافة عميل</span>
                </Button>
              </Link>
              <Link href="/dashboard/jobs">
                <Button
                  variant="outline"
                  className="h-16 md:h-20 flex flex-col gap-2 bg-transparent w-full text-xs md:text-sm"
                >
                  <Briefcase className="h-5 w-5 md:h-6 md:w-6" />
                  <span>إضافة وظيفة</span>
                </Button>
              </Link>
              <Link href="/dashboard/reports">
                <Button
                  variant="outline"
                  className="h-16 md:h-20 flex flex-col gap-2 bg-transparent w-full text-xs md:text-sm"
                >
                  <FileText className="h-5 w-5 md:h-6 md:w-6" />
                  <span>إنشاء تقرير</span>
                </Button>
              </Link>
              <Link href="/dashboard/candidates">
                <Button
                  variant="outline"
                  className="h-16 md:h-20 flex flex-col gap-2 bg-transparent w-full text-xs md:text-sm"
                >
                  <Calendar className="h-5 w-5 md:h-6 md:w-6" />
                  <span>جدولة مقابلة</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Details Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {selectedCard && getSelectedData()
                  ? stats.find((s) => s.id === selectedCard)?.title ||
                    monthlyTargets.find((t) => t.id === selectedCard)?.name
                  : "التفاصيل"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {selectedCard && getSelectedData() && (
                <div>
                  {stats.find((s) => s.id === selectedCard) && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(stats.find((s) => s.id === selectedCard)?.details || {}).map(([key, value]) => (
                          <div key={key} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="text-sm text-gray-600 dark:text-gray-300 capitalize">{key}</div>
                            <div className="text-lg font-semibold">
                              {Array.isArray(value) ? value.join(", ") : value}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  {monthlyTargets.find((t) => t.id === selectedCard) && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(monthlyTargets.find((t) => t.id === selectedCard)?.details || {}).map(
                          ([key, value]) => (
                            <div key={key} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                              <div className="text-sm text-gray-600 dark:text-gray-300 capitalize">{key}</div>
                              <div className="text-lg font-semibold">
                                {Array.isArray(value) ? value.join(", ") : value}
                              </div>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* AI Chatbot */}
        <AIChatbot />
      </div>
    </DashboardLayout>
  )
}
