"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Settings, Plus, Edit, Trash2, Move } from "lucide-react"

interface TimelineStage {
  id: string
  name: string
  description: string
  icon: string
  color: string
  required: boolean
  autoProgress: boolean
  estimatedDays: number
  actions: string[]
  notifications: string[]
  order: number
}

interface TimelineConfiguratorProps {
  isOpen: boolean
  onClose: () => void
  onSave: (stages: TimelineStage[]) => void
  currentStages: TimelineStage[]
}

export function TimelineConfigurator({ isOpen, onClose, onSave, currentStages }: TimelineConfiguratorProps) {
  const [stages, setStages] = useState<TimelineStage[]>(currentStages)
  const [editingStage, setEditingStage] = useState<TimelineStage | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const defaultStages: TimelineStage[] = [
    {
      id: "application",
      name: "استلام الطلب",
      description: "تم استلام طلب التوظيف من المرشح",
      icon: "📝",
      color: "bg-blue-100 text-blue-800",
      required: true,
      autoProgress: true,
      estimatedDays: 1,
      actions: ["مراجعة السيرة الذاتية", "تقييم أولي"],
      notifications: ["إشعار للمرشح", "إشعار لفريق التوظيف"],
      order: 1,
    },
    {
      id: "screening",
      name: "الفحص الأولي",
      description: "مراجعة السيرة الذاتية والتأهيل الأولي",
      icon: "🔍",
      color: "bg-yellow-100 text-yellow-800",
      required: true,
      autoProgress: false,
      estimatedDays: 2,
      actions: ["مراجعة المؤهلات", "فحص المهارات"],
      notifications: ["تحديث الحالة"],
      order: 2,
    },
    {
      id: "first_interview",
      name: "المقابلة الأولى",
      description: "مقابلة أولية مع فريق الموارد البشرية",
      icon: "👥",
      color: "bg-purple-100 text-purple-800",
      required: true,
      autoProgress: false,
      estimatedDays: 3,
      actions: ["جدولة المقابلة", "إجراء المقابلة", "تقييم الأداء"],
      notifications: ["دعوة للمقابلة", "تذكير قبل المقابلة"],
      order: 3,
    },
    {
      id: "technical_test",
      name: "الاختبار التقني",
      description: "تقييم المهارات التقنية للمرشح",
      icon: "💻",
      color: "bg-indigo-100 text-indigo-800",
      required: false,
      autoProgress: false,
      estimatedDays: 2,
      actions: ["إرسال الاختبار", "تقييم النتائج"],
      notifications: ["إرسال رابط الاختبار", "تذكير بالموعد النهائي"],
      order: 4,
    },
    {
      id: "client_interview",
      name: "مقابلة مع العميل",
      description: "مقابلة نهائية مع العميل أو صاحب العمل",
      icon: "🤝",
      color: "bg-green-100 text-green-800",
      required: true,
      autoProgress: false,
      estimatedDays: 5,
      actions: ["تنسيق الموعد", "إجراء المقابلة", "الحصول على التقييم"],
      notifications: ["تنسيق مع العميل", "دعوة المرشح"],
      order: 5,
    },
    {
      id: "offer",
      name: "عرض العمل",
      description: "إرسال عرض العمل الرسمي للمرشح",
      icon: "📄",
      color: "bg-orange-100 text-orange-800",
      required: true,
      autoProgress: false,
      estimatedDays: 2,
      actions: ["إعداد العرض", "إرسال العرض", "متابعة الرد"],
      notifications: ["إرسال العرض", "تذكير بالرد"],
      order: 6,
    },
    {
      id: "acceptance",
      name: "قبول العرض",
      description: "قبول المرشح لعرض العمل",
      icon: "✅",
      color: "bg-green-100 text-green-800",
      required: true,
      autoProgress: false,
      estimatedDays: 3,
      actions: ["تأكيد القبول", "إعداد العقد"],
      notifications: ["تأكيد القبول", "بدء الإجراءات"],
      order: 7,
    },
    {
      id: "onboarding",
      name: "التأهيل والإدماج",
      description: "عملية تأهيل الموظف الجديد",
      icon: "🎯",
      color: "bg-teal-100 text-teal-800",
      required: true,
      autoProgress: false,
      estimatedDays: 7,
      actions: ["إعداد الحساب", "التوجيه", "التدريب الأولي"],
      notifications: ["ترحيب بالموظف", "جدولة التوجيه"],
      order: 8,
    },
    {
      id: "probation",
      name: "فترة التجربة",
      description: "فترة التجربة للموظف الجديد",
      icon: "⏱️",
      color: "bg-amber-100 text-amber-800",
      required: false,
      autoProgress: false,
      estimatedDays: 90,
      actions: ["متابعة الأداء", "تقييم دوري", "تقييم نهائي"],
      notifications: ["بداية فترة التجربة", "تذكيرات دورية", "تقييم نهائي"],
      order: 9,
    },
    {
      id: "guarantee",
      name: "فترة الضمان",
      description: "فترة الضمان بعد التوظيف الناجح",
      icon: "🛡️",
      color: "bg-emerald-100 text-emerald-800",
      required: true,
      autoProgress: true,
      estimatedDays: 90,
      actions: ["متابعة الأداء", "تقييم الرضا", "تجديد أو إنهاء"],
      notifications: ["بداية فترة الضمان", "تقييمات دورية", "انتهاء الضمان"],
      order: 10,
    },
  ]

  const iconOptions = ["📝", "🔍", "👥", "💻", "🤝", "📄", "✅", "🎯", "⏱️", "🛡️", "📞", "📧", "🎥", "📊", "🏆"]

  const colorOptions = [
    { name: "أزرق", value: "bg-blue-100 text-blue-800" },
    { name: "أخضر", value: "bg-green-100 text-green-800" },
    { name: "أصفر", value: "bg-yellow-100 text-yellow-800" },
    { name: "بنفسجي", value: "bg-purple-100 text-purple-800" },
    { name: "أحمر", value: "bg-red-100 text-red-800" },
    { name: "برتقالي", value: "bg-orange-100 text-orange-800" },
    { name: "وردي", value: "bg-pink-100 text-pink-800" },
    { name: "رمادي", value: "bg-gray-100 text-gray-800" },
  ]

  const handleAddStage = () => {
    const newStage: TimelineStage = {
      id: `stage_${Date.now()}`,
      name: "مرحلة جديدة",
      description: "وصف المرحلة",
      icon: "📝",
      color: "bg-gray-100 text-gray-800",
      required: false,
      autoProgress: false,
      estimatedDays: 1,
      actions: [],
      notifications: [],
      order: stages.length + 1,
    }
    setEditingStage(newStage)
    setIsEditDialogOpen(true)
  }

  const handleEditStage = (stage: TimelineStage) => {
    setEditingStage({ ...stage })
    setIsEditDialogOpen(true)
  }

  const handleSaveStage = () => {
    if (!editingStage) return

    if (stages.find((s) => s.id === editingStage.id)) {
      setStages(stages.map((s) => (s.id === editingStage.id ? editingStage : s)))
    } else {
      setStages([...stages, editingStage])
    }

    setEditingStage(null)
    setIsEditDialogOpen(false)
  }

  const handleDeleteStage = (stageId: string) => {
    if (confirm("هل أنت متأكد من حذف هذه المرحلة؟")) {
      setStages(stages.filter((s) => s.id !== stageId))
    }
  }

  const handleMoveStage = (stageId: string, direction: "up" | "down") => {
    const stageIndex = stages.findIndex((s) => s.id === stageId)
    if (stageIndex === -1) return

    const newStages = [...stages]
    const targetIndex = direction === "up" ? stageIndex - 1 : stageIndex + 1

    if (targetIndex >= 0 && targetIndex < stages.length) {
      ;[newStages[stageIndex], newStages[targetIndex]] = [newStages[targetIndex], newStages[stageIndex]]
      // Update order numbers
      newStages.forEach((stage, index) => {
        stage.order = index + 1
      })
      setStages(newStages)
    }
  }

  const handleResetToDefault = () => {
    if (confirm("هل تريد إعادة تعيين المراحل إلى الإعدادات الافتراضية؟")) {
      setStages([...defaultStages])
    }
  }

  const handleSaveConfiguration = () => {
    onSave(stages)
    onClose()
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              إعدادات التايم لاين
            </DialogTitle>
            <DialogDescription>قم بتخصيص مراحل عملية التوظيف حسب احتياجاتك</DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button onClick={handleAddStage} className="bg-[#C0322D] hover:bg-[#C1352F]">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مرحلة
                </Button>
                <Button variant="outline" onClick={handleResetToDefault}>
                  إعادة تعيين افتراضي
                </Button>
              </div>
              <div className="text-sm text-gray-500">إجمالي المراحل: {stages.length}</div>
            </div>

            {/* Stages List */}
            <div className="space-y-3">
              {stages
                .sort((a, b) => a.order - b.order)
                .map((stage, index) => (
                  <Card key={stage.id} className="border-l-4 border-l-[#C0322D]">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="text-2xl">{stage.icon}</div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium">{stage.name}</h4>
                              <Badge className={stage.color}>المرحلة {stage.order}</Badge>
                              {stage.required && <Badge variant="outline">مطلوبة</Badge>}
                              {stage.autoProgress && <Badge variant="outline">تلقائية</Badge>}
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{stage.description}</p>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span>⏱️ {stage.estimatedDays} يوم</span>
                              <span>🔧 {stage.actions.length} إجراء</span>
                              <span>🔔 {stage.notifications.length} إشعار</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMoveStage(stage.id, "up")}
                            disabled={index === 0}
                          >
                            <Move className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => handleEditStage(stage)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteStage(stage.id)}
                            disabled={stage.required}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>

            {/* Save Actions */}
            <div className="flex justify-end space-x-2 rtl:space-x-reverse pt-4 border-t">
              <Button variant="outline" onClick={onClose}>
                إلغاء
              </Button>
              <Button onClick={handleSaveConfiguration} className="bg-[#C0322D] hover:bg-[#C1352F]">
                حفظ الإعدادات
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Stage Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{editingStage?.id.startsWith("stage_") ? "إضافة مرحلة جديدة" : "تعديل المرحلة"}</DialogTitle>
            <DialogDescription>قم بتخصيص تفاصيل المرحلة</DialogDescription>
          </DialogHeader>

          {editingStage && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stageName">اسم المرحلة</Label>
                  <Input
                    id="stageName"
                    value={editingStage.name}
                    onChange={(e) => setEditingStage({ ...editingStage, name: e.target.value })}
                    placeholder="اسم المرحلة"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="estimatedDays">المدة المتوقعة (أيام)</Label>
                  <Input
                    id="estimatedDays"
                    type="number"
                    value={editingStage.estimatedDays}
                    onChange={(e) =>
                      setEditingStage({ ...editingStage, estimatedDays: Number.parseInt(e.target.value) || 1 })
                    }
                    min="1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stageDescription">وصف المرحلة</Label>
                <Textarea
                  id="stageDescription"
                  value={editingStage.description}
                  onChange={(e) => setEditingStage({ ...editingStage, description: e.target.value })}
                  placeholder="وصف تفصيلي للمرحلة"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>الأيقونة</Label>
                  <Select
                    value={editingStage.icon}
                    onValueChange={(value) => setEditingStage({ ...editingStage, icon: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {iconOptions.map((icon) => (
                        <SelectItem key={icon} value={icon}>
                          <span className="text-lg">{icon}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>اللون</Label>
                  <Select
                    value={editingStage.color}
                    onValueChange={(value) => setEditingStage({ ...editingStage, color: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map((color) => (
                        <SelectItem key={color.value} value={color.value}>
                          <div className="flex items-center gap-2">
                            <div className={`w-4 h-4 rounded ${color.value.split(" ")[0]}`}></div>
                            {color.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>مرحلة مطلوبة</Label>
                    <p className="text-sm text-gray-600 dark:text-gray-300">لا يمكن تخطي هذه المرحلة</p>
                  </div>
                  <Switch
                    checked={editingStage.required}
                    onCheckedChange={(checked) => setEditingStage({ ...editingStage, required: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>التقدم التلقائي</Label>
                    <p className="text-sm text-gray-600 dark:text-gray-300">الانتقال التلقائي للمرحلة التالية</p>
                  </div>
                  <Switch
                    checked={editingStage.autoProgress}
                    onCheckedChange={(checked) => setEditingStage({ ...editingStage, autoProgress: checked })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>الإجراءات المطلوبة</Label>
                <div className="space-y-2">
                  {editingStage.actions.map((action, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={action}
                        onChange={(e) => {
                          const newActions = [...editingStage.actions]
                          newActions[index] = e.target.value
                          setEditingStage({ ...editingStage, actions: newActions })
                        }}
                        placeholder="إجراء مطلوب"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const newActions = editingStage.actions.filter((_, i) => i !== index)
                          setEditingStage({ ...editingStage, actions: newActions })
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setEditingStage({ ...editingStage, actions: [...editingStage.actions, "إجراء جديد"] })
                    }
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة إجراء
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>الإشعارات</Label>
                <div className="space-y-2">
                  {editingStage.notifications.map((notification, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={notification}
                        onChange={(e) => {
                          const newNotifications = [...editingStage.notifications]
                          newNotifications[index] = e.target.value
                          setEditingStage({ ...editingStage, notifications: newNotifications })
                        }}
                        placeholder="نوع الإشعار"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const newNotifications = editingStage.notifications.filter((_, i) => i !== index)
                          setEditingStage({ ...editingStage, notifications: newNotifications })
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setEditingStage({
                        ...editingStage,
                        notifications: [...editingStage.notifications, "إشعار جديد"],
                      })
                    }
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة إشعار
                  </Button>
                </div>
              </div>

              <div className="flex justify-end space-x-2 rtl:space-x-reverse pt-4 border-t">
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  إلغاء
                </Button>
                <Button onClick={handleSaveStage} className="bg-[#C0322D] hover:bg-[#C1352F]">
                  حفظ المرحلة
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
