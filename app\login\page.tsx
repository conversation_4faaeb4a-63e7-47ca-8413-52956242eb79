"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Eye, EyeOff, LogIn, User, Mail, Lock, Zap } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { useLanguage } from "@/hooks/use-language"

const demoAccounts = [
  {
    name: "أحمد المدير",
    username: "admin",
    email: "<EMAIL>",
    password: "admin123",
    role: "مدير النظام",
    color: "bg-red-100 text-red-800",
    description: "صلاحيات كاملة لإدارة النظام",
  },
  {
    name: "سارة الموارد البشرية",
    username: "hr",
    email: "<EMAIL>",
    password: "hr123",
    role: "موارد بشرية",
    color: "bg-blue-100 text-blue-800",
    description: "إدارة المستخدمين والتقارير والوظائف",
  },
  {
    name: "محمد المباحث",
    username: "recruiter",
    email: "<EMAIL>",
    password: "recruiter123",
    role: "مسؤول توظيف",
    color: "bg-green-100 text-green-800",
    description: "إدارة الوظائف والمرشحين",
  },
  {
    name: "شركة التقنية المتقدمة",
    username: "client",
    email: "<EMAIL>",
    password: "client123",
    role: "عميل",
    color: "bg-purple-100 text-purple-800",
    description: "نشر الوظائف ومراجعة المرشحين",
  },
  {
    name: "علي الباحث عن عمل",
    username: "jobseeker",
    email: "<EMAIL>",
    password: "jobseeker123",
    role: "باحث عن عمل",
    color: "bg-gray-100 text-gray-800",
    description: "البحث عن الوظائف وتقديم الطلبات",
  },
]

export default function LoginPage() {
  const [identifier, setIdentifier] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const { login, isAuthenticated } = useAuth()
  const { language, toggleLanguage } = useLanguage()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated) {
      router.push("/dashboard")
    }
  }, [isAuthenticated, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      const success = await login(identifier, password)
      if (success) {
        router.push("/dashboard")
      } else {
        setError("اسم المستخدم/البريد الإلكتروني أو كلمة المرور غير صحيحة")
      }
    } catch (error) {
      setError("حدث خطأ أثناء تسجيل الدخول")
    } finally {
      setIsLoading(false)
    }
  }

  const handleQuickLogin = (account: any) => {
    setIdentifier(account.username)
    setPassword(account.password)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Login Form */}
        <div className="flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-[#1A2A46] to-[#C1352F] rounded-lg flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
              </div>
              <CardTitle className="text-2xl font-bold">تسجيل الدخول</CardTitle>
              <CardDescription>أدخل بياناتك للوصول إلى لوحة التحكم</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="identifier">اسم المستخدم أو البريد الإلكتروني</Label>
                  <div className="relative">
                    <User className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="identifier"
                      type="text"
                      value={identifier}
                      onChange={(e) => setIdentifier(e.target.value)}
                      placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                      className="pr-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">كلمة المرور</Label>
                  <div className="relative">
                    <Lock className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="أدخل كلمة المرور"
                      className="pr-10 pl-10"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-0 top-0 h-full px-3"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <Button type="submit" className="w-full bg-[#C0322D] hover:bg-[#C1352F]" disabled={isLoading}>
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      جاري تسجيل الدخول...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <LogIn className="h-4 w-4" />
                      تسجيل الدخول
                    </div>
                  )}
                </Button>
              </form>

              <div className="mt-6 text-center">
                <Button variant="ghost" onClick={toggleLanguage} className="text-sm">
                  {language === "ar" ? "English" : "العربية"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Demo Accounts */}
        <div className="flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-xl">الحسابات التجريبية</CardTitle>
              <CardDescription>اختر حساباً للدخول السريع</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {demoAccounts.map((account, index) => (
                  <div
                    key={index}
                    className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                    onClick={() => handleQuickLogin(account)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{account.name}</h4>
                      <Badge className={account.color}>{account.role}</Badge>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">{account.description}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {account.username}
                      </div>
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {account.email}
                      </div>
                    </div>
                    <div className="text-xs text-gray-400 mt-1">كلمة المرور: {account.password}</div>
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <h4 className="font-medium text-sm text-blue-800 dark:text-blue-200 mb-2">ملاحظة:</h4>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  يمكنك تسجيل الدخول باستخدام اسم المستخدم أو البريد الإلكتروني مع كلمة المرور المقابلة. انقر على أي
                  حساب لملء البيانات تلقائياً.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
