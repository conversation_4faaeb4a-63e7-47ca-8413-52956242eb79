"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu, X, Globe, User, LogOut } from "lucide-react"
import { Logo } from "./logo"
import { useLanguage } from "@/hooks/use-language"
import { useAuth } from "@/hooks/use-auth"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { language, setLanguage, t } = useLanguage()
  const { user, logout, isAuthenticated } = useAuth()

  const toggleLanguage = () => {
    setLanguage(language === "ar" ? "en" : "ar")
  }

  const handleLogout = () => {
    logout()
    window.location.href = "/"
  }

  return (
    <header className="bg-white dark:bg-gray-900 shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Logo />

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
            <Link href="/" className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors">
              {t("home")}
            </Link>
            <Link href="#services" className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors">
              {t("services")}
            </Link>
            <Link href="#about" className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors">
              {t("about")}
            </Link>
            <Link href="#contact" className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors">
              {t("contact")}
            </Link>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4 rtl:space-x-reverse">
            <Button variant="ghost" size="sm" onClick={toggleLanguage} className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              {language === "ar" ? "EN" : "العربية"}
            </Button>

            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    {user?.name}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard">{t("dashboard")}</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="h-4 w-4 mr-2" />
                    {t("logout")}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button asChild>
                <Link href="/login">{t("login")}</Link>
              </Button>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 dark:text-gray-300"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <div className="flex flex-col space-y-4">
              <Link
                href="/"
                className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t("home")}
              </Link>
              <Link
                href="#services"
                className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t("services")}
              </Link>
              <Link
                href="#about"
                className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t("about")}
              </Link>
              <Link
                href="#contact"
                className="text-gray-700 dark:text-gray-300 hover:text-[#C1352F] transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                {t("contact")}
              </Link>

              <div className="flex items-center justify-between pt-4 border-t">
                <Button variant="ghost" size="sm" onClick={toggleLanguage} className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  {language === "ar" ? "EN" : "العربية"}
                </Button>

                {isAuthenticated ? (
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/dashboard">{t("dashboard")}</Link>
                    </Button>
                    <Button variant="ghost" size="sm" onClick={handleLogout}>
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Button size="sm" asChild>
                    <Link href="/login">{t("login")}</Link>
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
