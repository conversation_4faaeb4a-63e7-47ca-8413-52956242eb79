"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Calendar,
  User,
  Building,
  Phone,
  Mail,
  MessageSquare,
  Edit,
  Plus,
  Eye,
  FileText,
  Send,
  Video,
  PlayCircle,
  StopCircle,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  CameraOff,
  Users,
  Bell,
  CheckSquare,
  X
} from "lucide-react"

export default function TimelineManagementPage() {
  const [selectedApplication, setSelectedApplication] = useState<any>(null)
  const [isInterviewDialogOpen, setIsInterviewDialogOpen] = useState(false)
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [upcomingInterviews, setUpcomingInterviews] = useState<any[]>([])

  // بيانات وهمية للطلبات مع تحديثات فورية
  const [applications, setApplications] = useState([
    {
      id: 1,
      candidateName: "أحمد محمد السالم",
      candidateEmail: "<EMAIL>",
      candidatePhone: "+966 50 123 4567",
      jobTitle: "مهندس بترول أول",
      company: "أرامكو السعودية",
      appliedDate: "2024-01-15",
      currentStage: "interview_1",
      priority: "high",
      timeline: [
        {
          stage: "application",
          title: "تقديم الطلب",
          date: "2024-01-15",
          status: "completed",
          description: "تم تقديم الطلب بنجاح",
          notes: "السيرة الذاتية مطابقة للمتطلبات",
          automated: true
        },
        {
          stage: "screening",
          title: "المراجعة الأولية",
          date: "2024-01-18",
          status: "completed",
          description: "تمت مراجعة الطلب والموافقة عليه",
          notes: "مؤهلات ممتازة",
          automated: true
        },
        {
          stage: "interview_1",
          title: "المقابلة الأولى",
          date: "2024-01-25",
          time: "10:00",
          status: "scheduled",
          description: "مقابلة مع قسم الموارد البشرية",
          notes: "مجدولة في 25 يناير الساعة 10:00 صباحاً",
          automated: false,
          interviewLink: "https://meet.yasglobal.com/interview/1"
        },
        {
          stage: "interview_2",
          title: "المقابلة الثانية",
          date: null,
          status: "pending",
          description: "مقابلة مع المدير المباشر",
          notes: "",
          automated: false
        },
        {
          stage: "offer",
          title: "عرض العمل",
          date: null,
          status: "pending",
          description: "إرسال عرض العمل",
          notes: "",
          automated: true
        },
        {
          stage: "contract",
          title: "توقيع العقد",
          date: null,
          status: "pending",
          description: "توقيع عقد العمل",
          notes: "",
          automated: false
        },
        {
          stage: "guarantee",
          title: "فترة الضمان",
          date: null,
          status: "pending",
          description: "بداية فترة الضمان (3 أشهر)",
          notes: "",
          automated: true
        }
      ]
    },
    {
      id: 2,
      candidateName: "فاطمة عبدالله النمر",
      candidateEmail: "<EMAIL>",
      candidatePhone: "+966 55 987 6543",
      jobTitle: "محلل مالي",
      company: "البنك الأهلي",
      appliedDate: "2024-01-20",
      currentStage: "offer",
      priority: "medium",
      timeline: [
        {
          stage: "application",
          title: "تقديم الطلب",
          date: "2024-01-20",
          status: "completed",
          description: "تم تقديم الطلب بنجاح",
          notes: "",
          automated: true
        },
        {
          stage: "screening",
          title: "المراجعة الأولية",
          date: "2024-01-22",
          status: "completed",
          description: "تمت مراجعة الطلب والموافقة عليه",
          notes: "",
          automated: true
        },
        {
          stage: "interview_1",
          title: "المقابلة الأولى",
          date: "2024-01-24",
          status: "completed",
          description: "مقابلة مع قسم الموارد البشرية",
          notes: "أداء ممتاز",
          automated: false
        },
        {
          stage: "offer",
          title: "عرض العمل",
          date: "2024-01-26",
          status: "in_progress",
          description: "تم إرسال عرض العمل",
          notes: "في انتظار رد المرشح",
          automated: true
        }
      ]
    }
  ])

  // المقابلات القادمة
  useEffect(() => {
    const interviews = applications.flatMap(app => 
      app.timeline
        .filter(stage => stage.status === 'scheduled' && stage.date)
        .map(stage => ({
          ...stage,
          candidateName: app.candidateName,
          jobTitle: app.jobTitle,
          company: app.company,
          applicationId: app.id
        }))
    ).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    
    setUpcomingInterviews(interviews)
  }, [applications])

  const getStageIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case "in_progress":
        return <Clock className="w-5 h-5 text-blue-600" />
      case "scheduled":
        return <Calendar className="w-5 h-5 text-orange-600" />
      case "rejected":
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">مكتمل</Badge>
      case "in_progress":
        return <Badge className="bg-blue-100 text-blue-800">جاري</Badge>
      case "scheduled":
        return <Badge className="bg-orange-100 text-orange-800">مجدول</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800">مرفوض</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">في الانتظار</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-100 text-red-800">عالي</Badge>
      case "medium":
        return <Badge className="bg-yellow-100 text-yellow-800">متوسط</Badge>
      case "low":
        return <Badge className="bg-green-100 text-green-800">منخفض</Badge>
      default:
        return <Badge variant="secondary">عادي</Badge>
    }
  }

  const handleUpdateStage = (applicationId: number, stageIndex: number, newStatus: string) => {
    setApplications(prev => prev.map(app => {
      if (app.id === applicationId) {
        const updatedTimeline = [...app.timeline]
        updatedTimeline[stageIndex] = {
          ...updatedTimeline[stageIndex],
          status: newStatus,
          date: newStatus === 'completed' ? new Date().toISOString().split('T')[0] : updatedTimeline[stageIndex].date
        }
        
        // تحديث المرحلة الحالية
        let currentStage = app.currentStage
        if (newStatus === 'completed' && stageIndex < updatedTimeline.length - 1) {
          currentStage = updatedTimeline[stageIndex + 1].stage
          // تفعيل المرحلة التالية تلقائياً
          if (updatedTimeline[stageIndex + 1].automated) {
            updatedTimeline[stageIndex + 1].status = 'in_progress'
          }
        }
        
        return {
          ...app,
          timeline: updatedTimeline,
          currentStage
        }
      }
      return app
    }))
  }

  const handleScheduleInterview = (applicationId: number, stageIndex: number, date: string, time: string) => {
    setApplications(prev => prev.map(app => {
      if (app.id === applicationId) {
        const updatedTimeline = [...app.timeline]
        updatedTimeline[stageIndex] = {
          ...updatedTimeline[stageIndex],
          status: 'scheduled',
          date,
          time,
          interviewLink: `https://meet.yasglobal.com/interview/${Date.now()}`
        }
        return {
          ...app,
          timeline: updatedTimeline
        }
      }
      return app
    }))
  }

  const handleSendNotification = (application: any, type: string, message: string) => {
    // محاكاة إرسال الإشعارات
    console.log(`Sending ${type} to ${application.candidateName}: ${message}`)
    alert(`تم إرسال ${type === 'email' ? 'بريد إلكتروني' : type === 'sms' ? 'رسالة نصية' : 'إشعار'} إلى ${application.candidateName}`)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">إدارة التايم لاين الذكي</h1>
            <p className="text-gray-600">تحكم شامل في مراحل التوظيف والمقابلات</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="gap-2">
              <Bell className="w-4 h-4" />
              الإشعارات ({upcomingInterviews.length})
            </Button>
            <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-red-600 hover:bg-red-700 gap-2">
                  <Calendar className="w-4 h-4" />
                  جدولة مقابلة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>جدولة مقابلة جديدة</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>المرشح</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر المرشح" />
                      </SelectTrigger>
                      <SelectContent>
                        {applications.map(app => (
                          <SelectItem key={app.id} value={app.id.toString()}>
                            {app.candidateName} - {app.jobTitle}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>التاريخ</Label>
                      <Input type="date" />
                    </div>
                    <div className="space-y-2">
                      <Label>الوقت</Label>
                      <Input type="time" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>ملاحظات</Label>
                    <Textarea placeholder="ملاحظات إضافية..." rows={2} />
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button className="bg-red-600 hover:bg-red-700 flex-1" onClick={() => setIsScheduleDialogOpen(false)}>
                      جدولة المقابلة
                    </Button>
                    <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)}>
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Tabs defaultValue="timeline" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="timeline">التايم لاين</TabsTrigger>
            <TabsTrigger value="interviews">المقابلات ({upcomingInterviews.length})</TabsTrigger>
            <TabsTrigger value="analytics">التحليلات</TabsTrigger>
          </TabsList>

          <TabsContent value="timeline" className="space-y-6">
            <div className="grid gap-6">
              {applications.map((application) => (
                <Card key={application.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <User className="w-5 h-5" />
                          {application.candidateName}
                          {getPriorityBadge(application.priority)}
                        </CardTitle>
                        <CardDescription className="flex items-center gap-4 mt-2">
                          <span className="flex items-center gap-1">
                            <Building className="w-4 h-4" />
                            {application.company}
                          </span>
                          <span>{application.jobTitle}</span>
                          <span className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {application.appliedDate}
                          </span>
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleSendNotification(application, 'email', 'تحديث حالة الطلب')}>
                          <Mail className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleSendNotification(application, 'sms', 'تذكير بالمقابلة')}>
                          <MessageSquare className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {application.timeline.map((stage, index) => (
                        <div key={stage.stage} className="flex items-start gap-4 p-4 border rounded-lg">
                          <div className="flex flex-col items-center">
                            {getStageIcon(stage.status)}
                            {index < application.timeline.length - 1 && (
                              <div className="w-px h-8 bg-gray-300 mt-2"></div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">{stage.title}</h4>
                              <div className="flex items-center gap-2">
                                {getStatusBadge(stage.status)}
                                {stage.date && (
                                  <span className="text-sm text-gray-500">
                                    {stage.date} {stage.time && `- ${stage.time}`}
                                  </span>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{stage.description}</p>
                            {stage.notes && (
                              <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded mb-2">
                                {stage.notes}
                              </p>
                            )}
                            
                            {/* أزرار التحكم */}
                            <div className="flex gap-2 mt-3">
                              {stage.status === 'pending' && (
                                <>
                                  <Button 
                                    size="sm" 
                                    className="bg-green-600 hover:bg-green-700"
                                    onClick={() => handleUpdateStage(application.id, index, 'in_progress')}
                                  >
                                    <CheckSquare className="w-4 h-4 mr-1" />
                                    بدء
                                  </Button>
                                  {stage.stage.includes('interview') && (
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      onClick={() => {
                                        // فتح dialog جدولة المقابلة
                                        setSelectedApplication(application)
                                        setIsScheduleDialogOpen(true)
                                      }}
                                    >
                                      <Calendar className="w-4 h-4 mr-1" />
                                      جدولة
                                    </Button>
                                  )}
                                </>
                              )}
                              
                              {stage.status === 'in_progress' && (
                                <>
                                  <Button 
                                    size="sm" 
                                    className="bg-green-600 hover:bg-green-700"
                                    onClick={() => handleUpdateStage(application.id, index, 'completed')}
                                  >
                                    <CheckCircle className="w-4 h-4 mr-1" />
                                    إكمال
                                  </Button>
                                  <Button 
                                    size="sm" 
                                    variant="destructive"
                                    onClick={() => handleUpdateStage(application.id, index, 'rejected')}
                                  >
                                    <X className="w-4 h-4 mr-1" />
                                    رفض
                                  </Button>
                                </>
                              )}
                              
                              {stage.status === 'scheduled' && stage.interviewLink && (
                                <Button 
                                  size="sm" 
                                  className="bg-blue-600 hover:bg-blue-700"
                                  onClick={() => {
                                    setSelectedApplication(application)
                                    setIsInterviewDialogOpen(true)
                                  }}
                                >
                                  <Video className="w-4 h-4 mr-1" />
                                  بدء المقابلة
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="interviews" className="space-y-6">
            <div className="grid gap-4">
              {upcomingInterviews.map((interview, index) => (
                <Card key={index} className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold">{interview.candidateName}</h3>
                      <p className="text-sm text-gray-600">{interview.jobTitle} - {interview.company}</p>
                      <p className="text-sm text-gray-500">{interview.date} في {interview.time}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                        <Video className="w-4 h-4" />
                        انضمام
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                      <p className="text-2xl font-bold">{applications.length}</p>
                    </div>
                    <FileText className="w-8 h-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">المقابلات المجدولة</p>
                      <p className="text-2xl font-bold text-orange-600">{upcomingInterviews.length}</p>
                    </div>
                    <Calendar className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">معدل الإكمال</p>
                      <p className="text-2xl font-bold text-green-600">75%</p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Interview Dialog */}
        <Dialog open={isInterviewDialogOpen} onOpenChange={setIsInterviewDialogOpen}>
          <DialogContent className="max-w-4xl h-[80vh]">
            <DialogHeader>
              <DialogTitle>مقابلة مع {selectedApplication?.candidateName}</DialogTitle>
            </DialogHeader>
            <div className="flex-1 bg-gray-900 rounded-lg relative">
              <div className="absolute inset-0 flex items-center justify-center text-white">
                <div className="text-center">
                  <Video className="w-16 h-16 mx-auto mb-4" />
                  <p className="text-lg">مقابلة فيديو</p>
                  <p className="text-sm opacity-75">انقر لبدء المقابلة</p>
                </div>
              </div>
              
              {/* أدوات التحكم */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-4">
                <Button size="sm" variant="outline" className="bg-white/20 text-white border-white/30">
                  <Mic className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="outline" className="bg-white/20 text-white border-white/30">
                  <Camera className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="destructive">
                  <StopCircle className="w-4 h-4" />
                  إنهاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
