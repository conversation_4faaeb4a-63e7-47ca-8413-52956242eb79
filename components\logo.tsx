"use client"

import Link from "next/link"
import { useSettings } from "@/hooks/use-settings"

export function Logo() {
  const { settings } = useSettings()

  return (
    <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse">
      <div className="w-12 h-12 bg-gradient-to-br from-[#1A2A46] to-[#C1352F] rounded-lg flex items-center justify-center">
        <span className="text-white font-bold text-xl">YG</span>
      </div>
      <div className="flex flex-col">
        <span className="text-xl font-bold text-gray-900 dark:text-white">{settings.siteName}</span>
        <span className="text-sm text-[#F28C86]">Global Partner</span>
      </div>
    </Link>
  )
}
