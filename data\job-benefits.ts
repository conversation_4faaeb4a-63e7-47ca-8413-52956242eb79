export interface JobBenefit {
  id: string
  name: string
  category: 'health' | 'financial' | 'time' | 'development' | 'lifestyle' | 'other'
  description: string
  icon: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export const defaultJobBenefits: JobBenefit[] = [
  // Health Benefits
  {
    id: 'health-insurance',
    name: 'التأمين الطبي الشامل',
    category: 'health',
    description: 'تأمين طبي شامل للموظف وعائلته',
    icon: 'Heart',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'dental-insurance',
    name: 'تأمين الأسنان',
    category: 'health',
    description: 'تغطية تأمينية لعلاج الأسنان',
    icon: 'Smile',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'vision-insurance',
    name: 'تأمين النظر',
    category: 'health',
    description: 'تغطية تأمينية للعيون والنظارات',
    icon: 'Eye',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  
  // Financial Benefits
  {
    id: 'annual-bonus',
    name: 'مكافأة سنوية',
    category: 'financial',
    description: 'مكافأة سنوية بناءً على الأداء',
    icon: 'DollarSign',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'performance-bonus',
    name: 'مكافأة الأداء',
    category: 'financial',
    description: 'مكافآت شهرية أو ربع سنوية حسب الأداء',
    icon: 'TrendingUp',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'retirement-plan',
    name: 'نظام التقاعد',
    category: 'financial',
    description: 'مساهمة الشركة في نظام التقاعد',
    icon: 'PiggyBank',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'stock-options',
    name: 'خيارات الأسهم',
    category: 'financial',
    description: 'إمكانية شراء أسهم الشركة بسعر مخفض',
    icon: 'BarChart3',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  // Time Benefits
  {
    id: 'flexible-hours',
    name: 'ساعات عمل مرنة',
    category: 'time',
    description: 'مرونة في تحديد ساعات العمل',
    icon: 'Clock',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'remote-work',
    name: 'العمل عن بُعد',
    category: 'time',
    description: 'إمكانية العمل من المنزل أو أي مكان',
    icon: 'Home',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'paid-vacation',
    name: 'إجازة سنوية مدفوعة',
    category: 'time',
    description: 'إجازة سنوية مدفوعة الأجر',
    icon: 'Calendar',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'sick-leave',
    name: 'إجازة مرضية',
    category: 'time',
    description: 'إجازة مرضية مدفوعة الأجر',
    icon: 'Thermometer',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  // Development Benefits
  {
    id: 'training-budget',
    name: 'ميزانية التدريب',
    category: 'development',
    description: 'ميزانية سنوية للتدريب والتطوير المهني',
    icon: 'BookOpen',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'conference-attendance',
    name: 'حضور المؤتمرات',
    category: 'development',
    description: 'تغطية تكاليف حضور المؤتمرات المهنية',
    icon: 'Users',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'certification-support',
    name: 'دعم الشهادات المهنية',
    category: 'development',
    description: 'تغطية تكاليف الحصول على الشهادات المهنية',
    icon: 'Award',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  // Lifestyle Benefits
  {
    id: 'gym-membership',
    name: 'عضوية النادي الرياضي',
    category: 'lifestyle',
    description: 'عضوية مجانية أو مخفضة في النادي الرياضي',
    icon: 'Dumbbell',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'transportation',
    name: 'بدل المواصلات',
    category: 'lifestyle',
    description: 'بدل شهري للمواصلات أو توفير مواصلات الشركة',
    icon: 'Car',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'meal-allowance',
    name: 'بدل الوجبات',
    category: 'lifestyle',
    description: 'بدل يومي للوجبات أو وجبات مجانية في الشركة',
    icon: 'Coffee',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'phone-allowance',
    name: 'بدل الهاتف',
    category: 'lifestyle',
    description: 'بدل شهري لفاتورة الهاتف أو هاتف شركة',
    icon: 'Phone',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  // Other Benefits
  {
    id: 'life-insurance',
    name: 'تأمين على الحياة',
    category: 'other',
    description: 'تأمين على الحياة للموظف',
    icon: 'Shield',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'employee-discount',
    name: 'خصم الموظفين',
    category: 'other',
    description: 'خصومات على منتجات وخدمات الشركة',
    icon: 'Percent',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]

export const benefitCategories = [
  { id: 'health', name: 'الصحة والتأمين', icon: 'Heart' },
  { id: 'financial', name: 'المالية والمكافآت', icon: 'DollarSign' },
  { id: 'time', name: 'الوقت والإجازات', icon: 'Clock' },
  { id: 'development', name: 'التطوير المهني', icon: 'BookOpen' },
  { id: 'lifestyle', name: 'نمط الحياة', icon: 'Coffee' },
  { id: 'other', name: 'أخرى', icon: 'Plus' },
] as const
