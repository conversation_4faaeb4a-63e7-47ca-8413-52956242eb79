"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
  Building2,
  Users,
  Briefcase,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  UserCheck,
  UserX,
  AlertCircle
} from "lucide-react"

export default function ClientDetailsPage() {
  const params = useParams()
  const clientId = params.id

  // بيانات وهمية للشركة
  const clientData = {
    id: clientId,
    name: "أرامكو السعودية",
    logo: "/api/placeholder/100/100",
    industry: "البترول والغاز",
    location: "الظهران، المملكة العربية السعودية",
    phone: "+966 13 872 7000",
    email: "<EMAIL>",
    website: "www.aramco.com",
    establishedYear: 1933,
    employeeCount: "70,000+",
    description: "شركة أرامكو السعودية هي شركة البترول الوطنية للمملكة العربية السعودية وواحدة من أكبر شركات النفط في العالم."
  }

  // بيانات وهمية للوظائف
  const jobs = [
    {
      id: 1,
      title: "مهندس بترول أول",
      department: "الهندسة",
      location: "الظهران",
      type: "دائم",
      salary: "25000 - 35000 ر.س",
      postedDate: "2024-01-15",
      deadline: "2024-02-15",
      applicants: 45,
      accepted: 12,
      rejected: 20,
      pending: 13,
      status: "نشط"
    },
    {
      id: 2,
      title: "محلل مالي",
      department: "المالية",
      location: "الرياض",
      type: "دائم",
      salary: "18000 - 25000 ر.س",
      postedDate: "2024-01-20",
      deadline: "2024-02-20",
      applicants: 32,
      accepted: 8,
      rejected: 15,
      pending: 9,
      status: "نشط"
    },
    {
      id: 3,
      title: "مطور برمجيات",
      department: "تقنية المعلومات",
      location: "الخبر",
      type: "مؤقت",
      salary: "20000 - 28000 ر.س",
      postedDate: "2024-01-10",
      deadline: "2024-02-10",
      applicants: 28,
      accepted: 5,
      rejected: 18,
      pending: 5,
      status: "مكتمل"
    }
  ]

  // بيانات وهمية للمتقدمين المقبولين
  const acceptedCandidates = [
    {
      id: 1,
      name: "أحمد محمد السالم",
      email: "<EMAIL>",
      phone: "+966 50 123 4567",
      position: "مهندس بترول أول",
      hireDate: "2024-02-01",
      salary: "30000 ر.س",
      status: "نشط",
      experience: "8 سنوات",
      education: "بكالوريوس هندسة بترول"
    },
    {
      id: 2,
      name: "فاطمة عبدالله النمر",
      email: "<EMAIL>",
      phone: "+966 55 987 6543",
      position: "محلل مالي",
      hireDate: "2024-01-25",
      salary: "22000 ر.س",
      status: "نشط",
      experience: "5 سنوات",
      education: "بكالوريوس محاسبة"
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "نشط":
        return <Badge className="bg-green-100 text-green-800">{status}</Badge>
      case "مكتمل":
        return <Badge className="bg-blue-100 text-blue-800">{status}</Badge>
      case "منتهي":
        return <Badge className="bg-red-100 text-red-800">{status}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Client Header */}
        <div className="flex items-start gap-6 p-6 bg-white rounded-lg border">
          <div className="w-24 h-24 bg-red-100 rounded-lg flex items-center justify-center">
            <Building2 className="w-12 h-12 text-red-600" />
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">{clientData.name}</h1>
            <p className="text-gray-600 mb-4">{clientData.description}</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span>{clientData.location}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span>{clientData.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-500" />
                <span>{clientData.email}</span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">تأسست عام</div>
            <div className="text-2xl font-bold">{clientData.establishedYear}</div>
            <div className="text-sm text-gray-500 mt-2">عدد الموظفين</div>
            <div className="text-lg font-semibold">{clientData.employeeCount}</div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الوظائف</p>
                  <p className="text-2xl font-bold">{jobs.length}</p>
                </div>
                <Briefcase className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المتقدمين</p>
                  <p className="text-2xl font-bold">{jobs.reduce((sum, job) => sum + job.applicants, 0)}</p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المقبولين</p>
                  <p className="text-2xl font-bold text-green-600">{jobs.reduce((sum, job) => sum + job.accepted, 0)}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المرفوضين</p>
                  <p className="text-2xl font-bold text-red-600">{jobs.reduce((sum, job) => sum + job.rejected, 0)}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs for Jobs and Candidates */}
        <Tabs defaultValue="jobs" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="jobs">الوظائف المتاحة</TabsTrigger>
            <TabsTrigger value="candidates">المرشحين المقبولين</TabsTrigger>
          </TabsList>
          
          <TabsContent value="jobs" className="space-y-6">
            <div className="grid gap-6">
              {jobs.map((job) => (
                <Card key={job.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold">{job.title}</h3>
                        <p className="text-gray-600">{job.department} • {job.location}</p>
                        <p className="text-sm text-gray-500 mt-1">{job.salary}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(job.status)}
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>تفاصيل الوظيفة: {job.title}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-500">القسم</label>
                                  <p>{job.department}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-500">الموقع</label>
                                  <p>{job.location}</p>
                                </div>
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-500">الراتب</label>
                                  <p>{job.salary}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-500">نوع العقد</label>
                                  <p>{job.type}</p>
                                </div>
                              </div>
                              <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded">
                                <div className="text-center">
                                  <p className="text-2xl font-bold text-blue-600">{job.applicants}</p>
                                  <p className="text-sm text-gray-500">متقدم</p>
                                </div>
                                <div className="text-center">
                                  <p className="text-2xl font-bold text-green-600">{job.accepted}</p>
                                  <p className="text-sm text-gray-500">مقبول</p>
                                </div>
                                <div className="text-center">
                                  <p className="text-2xl font-bold text-red-600">{job.rejected}</p>
                                  <p className="text-sm text-gray-500">مرفوض</p>
                                </div>
                                <div className="text-center">
                                  <p className="text-2xl font-bold text-orange-600">{job.pending}</p>
                                  <p className="text-sm text-gray-500">في الانتظار</p>
                                </div>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">تاريخ النشر</p>
                        <p className="font-medium">{job.postedDate}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">آخر موعد</p>
                        <p className="font-medium">{job.deadline}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">المتقدمين</p>
                        <p className="font-medium text-blue-600">{job.applicants}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">المقبولين</p>
                        <p className="font-medium text-green-600">{job.accepted}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">المرفوضين</p>
                        <p className="font-medium text-red-600">{job.rejected}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="candidates" className="space-y-6">
            <div className="grid gap-6">
              {acceptedCandidates.map((candidate) => (
                <Card key={candidate.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                          <UserCheck className="w-6 h-6 text-green-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold">{candidate.name}</h3>
                          <p className="text-gray-600">{candidate.position}</p>
                          <p className="text-sm text-gray-500">{candidate.education}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">{candidate.status}</Badge>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>تفاصيل المرشح: {candidate.name}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-500">البريد الإلكتروني</label>
                                  <p>{candidate.email}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-500">رقم الهاتف</label>
                                  <p>{candidate.phone}</p>
                                </div>
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-500">المنصب</label>
                                  <p>{candidate.position}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-500">الراتب</label>
                                  <p>{candidate.salary}</p>
                                </div>
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-500">سنوات الخبرة</label>
                                  <p>{candidate.experience}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-500">تاريخ التوظيف</label>
                                  <p>{candidate.hireDate}</p>
                                </div>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">البريد الإلكتروني</p>
                        <p className="font-medium">{candidate.email}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">الهاتف</p>
                        <p className="font-medium">{candidate.phone}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">الخبرة</p>
                        <p className="font-medium">{candidate.experience}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">تاريخ التوظيف</p>
                        <p className="font-medium">{candidate.hireDate}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
