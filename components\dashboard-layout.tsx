"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useCompanyName } from "@/components/company-name-provider"
import { Button } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Menu,
  Home,
  Users,
  Briefcase,
  UserCheck,
  BarChart3,
  Settings,
  Bell,
  LogOut,
  User,
  Building2,
  FileText,
  Globe,
  Zap,
  ChevronLeft,
  ChevronRight,
  Video,
  Monitor
} from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { useLanguage } from "@/hooks/use-language"
import { useSettings } from "@/hooks/use-settings"
import { AIChatbot } from "@/components/ai-chatbot"

interface NavItem {
  title: string
  href: string
  icon: any
  badge?: string
  permissions: string[]
  roles?: string[]
}

const navigationItems: NavItem[] = [
  {
    title: "لوحة التحكم",
    href: "/dashboard",
    icon: Home,
    permissions: ["all", "dashboard"],
  },
  {
    title: "إدارة المستخدمين",
    href: "/dashboard/users",
    icon: Users,
    permissions: ["users"],
    roles: ["admin", "hr"],
  },
  {
    title: "إدارة الوظائف",
    href: "/dashboard/jobs",
    icon: Briefcase,
    permissions: ["jobs"],
    roles: ["admin", "hr", "recruiter"],
  },
  {
    title: "المرشحين",
    href: "/dashboard/candidates",
    icon: UserCheck,
    permissions: ["candidates"],
    roles: ["admin", "hr", "recruiter"],
  },
  {
    title: "إدارة العملاء",
    href: "/dashboard/clients",
    icon: Building2,
    permissions: ["clients"],
    roles: ["admin", "hr"],
  },
  {
    title: "عقود الشركة",
    href: "/dashboard/contracts",
    icon: FileText,
    permissions: ["contracts"],
    roles: ["admin", "hr"],
  },
  {
    title: "عقود الموظفين",
    href: "/dashboard/employee-contracts",
    icon: Users,
    permissions: ["employee_contracts"],
    roles: ["admin", "hr"],
  },
  {
    title: "التقارير",
    href: "/dashboard/reports",
    icon: BarChart3,
    permissions: ["reports"],
    roles: ["admin", "hr"],
  },
  {
    title: "الإشعارات",
    href: "/dashboard/notifications",
    icon: Bell,
    permissions: ["notifications"],
    roles: ["admin", "hr", "recruiter"],
  },
  {
    title: "بوابة العميل",
    href: "/dashboard/client-portal",
    icon: Building2,
    permissions: ["client_portal"],
    roles: ["client"],
  },
  {
    title: "طلباتي",
    href: "/dashboard/applications",
    icon: FileText,
    permissions: ["applications"],
    roles: ["job_seeker"],
  },
  {
    title: "الوظائف المتاحة",
    href: "/dashboard/available-jobs",
    icon: Briefcase,
    permissions: ["view_jobs"],
    roles: ["job_seeker"],
  },
  {
    title: "تايم لاين التوظيف",
    href: "/dashboard/application-timeline",
    icon: FileText,
    permissions: ["timeline"],
    roles: ["admin", "hr"],
  },
  {
    title: "إدارة التايم لاين",
    href: "/dashboard/timeline-management",
    icon: Settings,
    permissions: ["timeline_management"],
    roles: ["admin", "hr"],
  },
  {
    title: "المقابلات الأونلاين",
    href: "/dashboard/video-interview",
    icon: Video,
    permissions: ["video_interview"],
    roles: ["admin", "hr"],
  },
  {
    title: "الملف الشخصي",
    href: "/dashboard/profile",
    icon: User,
    permissions: ["profile"],
  },
  {
    title: "الإعدادات",
    href: "/dashboard/settings",
    icon: Settings,
    permissions: ["settings"],
    roles: ["admin", "hr"],
  },
]

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { user, logout, hasPermission, hasRole } = useAuth()
  const { language, toggleLanguage } = useLanguage()
  const { settings } = useSettings()
  const { companyName } = useCompanyName()
  const pathname = usePathname()
  const router = useRouter()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!user) {
      router.push("/login")
    }
  }, [user, router])

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const handleLogout = () => {
    logout()
    router.push("/login")
  }

  // Filter navigation items based on user permissions and role
  const filteredNavItems = navigationItems.filter((item) => {
    const hasRequiredPermission = item.permissions.some((permission) => hasPermission(permission))
    const hasRequiredRole = !item.roles || item.roles.some((role) => hasRole(role as any))
    return hasRequiredPermission && hasRequiredRole
  })

  const NavItems = ({ mobile = false }: { mobile?: boolean }) => (
    <nav className={`space-y-2 ${mobile ? "px-4" : ""}`}>
      {filteredNavItems.map((item) => {
        const isActive = pathname === item.href
        return (
          <Link
            key={item.href}
            href={item.href}
            className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              isActive
                ? "bg-primary text-primary-foreground"
                : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
            }`}
            onClick={() => mobile && setIsMobileMenuOpen(false)}
          >
            <item.icon className="h-4 w-4" />
            {!isSidebarCollapsed && (
              <>
                {item.title}
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </>
            )}
          </Link>
        )
      })}
    </nav>
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center gap-4">
            {/* Mobile menu trigger */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="md:hidden">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-64 p-0">
                <div className="p-4 border-b">
                  <h2 className="font-semibold text-lg">{companyName}</h2>
                </div>
                <div className="py-4">
                  <NavItems mobile />
                </div>
              </SheetContent>
            </Sheet>

            {/* Desktop sidebar toggle */}
            <Button
              variant="ghost"
              size="sm"
              className="hidden md:flex"
              onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
            >
              {isSidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
            </Button>

            {/* Logo */}
            <Link href="/dashboard" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Zap className="h-5 w-5 text-primary-foreground" />
              </div>
              <span className="font-bold text-lg hidden sm:block">{companyName}</span>
            </Link>
          </div>

          <div className="flex items-center gap-4">
            {/* Language Toggle */}
            <Button variant="ghost" size="sm" onClick={toggleLanguage}>
              <Globe className="h-4 w-4 mr-2" />
              {language === "ar" ? "EN" : "ع"}
            </Button>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-primary-foreground" />
                  </div>
                  <div className="hidden sm:block text-right">
                    <div className="text-sm font-medium">{user.name}</div>
                    <div className="text-xs text-gray-500">{user.role}</div>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>
                  <div>
                    <div className="font-medium">{user.name}</div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {hasPermission("settings") && (
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard/settings">
                      <Settings className="h-4 w-4 mr-2" />
                      الإعدادات
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                  <LogOut className="h-4 w-4 mr-2" />
                  تسجيل الخروج
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar - Desktop */}
        <aside
          className={`hidden md:flex ${isSidebarCollapsed ? "w-16" : "w-64"} bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 min-h-[calc(100vh-73px)] transition-all duration-300`}
        >
          <div className="flex flex-col w-full p-4">
            <NavItems />
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 min-h-[calc(100vh-73px)]">{children}</main>
      </div>
      <AIChatbot />
    </div>
  )
}
