"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MessageCircle, Send, Bot, User, X, Minimize2, Maximize2 } from "lucide-react"

interface Message {
  id: string
  type: "user" | "bot"
  content: string
  timestamp: Date
}

interface QuickAction {
  id: string
  label: string
  action: string
}

const quickActions: QuickAction[] = [
  { id: "1", label: "إحصائيات الوظائف", action: "show_job_stats" },
  { id: "2", label: "المرشحين الجدد", action: "show_new_candidates" },
  { id: "3", label: "التقارير الشهرية", action: "show_monthly_reports" },
  { id: "4", label: "مساعدة في النظام", action: "system_help" },
]

export function AIChatbot() {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "bot",
      content: "مرحباً! أنا مساعدك الذكي في نظام ياس العالمية للشراكة. كيف يمكنني مساعدتك اليوم؟",
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setIsTyping(true)

    // Simulate AI response
    setTimeout(
      () => {
        const botResponse = generateBotResponse(inputValue)
        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: "bot",
          content: botResponse,
          timestamp: new Date(),
        }
        setMessages((prev) => [...prev, botMessage])
        setIsTyping(false)
      },
      1000 + Math.random() * 2000,
    )
  }

  const handleQuickAction = (action: string) => {
    const responses: { [key: string]: string } = {
      show_job_stats:
        "إليك إحصائيات الوظائف الحالية:\n\n• الوظائف النشطة: 23\n• المتقدمين الجدد: 156\n• المقابلات المجدولة: 8\n• العروض المرسلة: 5",
      show_new_candidates:
        "المرشحين الجدد هذا الأسبوع:\n\n• سارة أحمد - مطورة React\n• محمد علي - مصمم UI/UX\n• فاطمة السعد - مهندسة مدنية\n• أحمد محمد - مدير مشاريع",
      show_monthly_reports:
        "التقارير الشهرية متاحة في قسم التقارير. يمكنك الوصول إليها من القائمة الجانبية أو يمكنني مساعدتك في إنشاء تقرير مخصص.",
      system_help:
        "يمكنني مساعدتك في:\n\n• التنقل في النظام\n• إنشاء التقارير\n• إدارة الوظائف والمرشحين\n• الإعدادات والتخصيص\n\nما الذي تحتاج مساعدة فيه تحديداً؟",
    }

    const response = responses[action] || "عذراً، لم أتمكن من فهم طلبك. يمكنك إعادة صياغته؟"

    const botMessage: Message = {
      id: Date.now().toString(),
      type: "bot",
      content: response,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, botMessage])
  }

  const generateBotResponse = (userInput: string): string => {
    const input = userInput.toLowerCase()

    if (input.includes("وظيفة") || input.includes("job")) {
      return "يمكنني مساعدتك في إدارة الوظائف! يمكنك:\n\n• إضافة وظيفة جديدة من صفحة إدارة الوظائف\n• تعديل الوظائف الموجودة\n• مراجعة المتقدمين لكل وظيفة\n• تصدير تقارير الوظائف\n\nهل تحتاج مساعدة في شيء محدد؟"
    }

    if (input.includes("مرشح") || input.includes("candidate")) {
      return "بخصوص إدارة المرشحين، يمكنك:\n\n• مراجعة جميع المرشحين في صفحة المرشحين\n• تتبع مراحل كل مرشح\n• جدولة المقابلات\n• إدارة فترات الضمان\n• تصدير تقارير المرشحين\n\nأي معلومات إضافية تحتاجها؟"
    }

    if (input.includes("تقرير") || input.includes("report")) {
      return "يمكنك إنشاء تقارير مفصلة من صفحة التقارير:\n\n• تقارير الوظائف والمتقدمين\n• تقارير الأداء الشهرية\n• تقارير العملاء والإيرادات\n• تصدير بصيغة Excel أو PDF\n\nهل تريد مساعدة في إنشاء تقرير معين؟"
    }

    if (input.includes("مساعدة") || input.includes("help")) {
      return "أنا هنا لمساعدتك! يمكنني الإجابة على أسئلتك حول:\n\n• كيفية استخدام النظام\n• إدارة الوظائف والمرشحين\n• إنشاء التقارير\n• الإعدادات والتخصيص\n• حل المشاكل التقنية\n\nما الذي تحتاج مساعدة فيه؟"
    }

    if (input.includes("شكر") || input.includes("thank")) {
      return "العفو! أنا سعيد لمساعدتك. إذا كان لديك أي أسئلة أخرى، لا تتردد في سؤالي. أنا متاح دائماً لمساعدتك في استخدام النظام بأفضل طريقة ممكنة! 😊"
    }

    // Default response
    return "شكراً لك على سؤالك. يمكنني مساعدتك في:\n\n• إدارة الوظائف والمرشحين\n• إنشاء التقارير\n• التنقل في النظام\n• الإعدادات والتخصيص\n\nيمكنك أيضاً استخدام الأزرار السريعة أدناه للوصول السريع للمعلومات الشائعة."
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 left-4 md:bottom-6 md:left-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-12 h-12 md:w-14 md:h-14 bg-[#C0322D] hover:bg-[#C1352F] shadow-lg"
        >
          <MessageCircle className="h-5 w-5 md:h-6 md:w-6" />
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 left-4 md:bottom-6 md:left-6 z-50">
      <Card
        className={`w-80 md:w-96 shadow-2xl transition-all duration-300 ${isMinimized ? "h-16" : "h-[500px] md:h-[600px]"}`}
      >
        <CardHeader className="p-3 md:p-4 bg-[#C0322D] text-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4 md:h-5 md:w-5" />
              <CardTitle className="text-base md:text-lg">المساعد الذكي</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white hover:bg-white/20 p-1 h-6 w-6 md:h-8 md:w-8"
              >
                {isMinimized ? (
                  <Maximize2 className="h-3 w-3 md:h-4 md:w-4" />
                ) : (
                  <Minimize2 className="h-3 w-3 md:h-4 md:w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-white/20 p-1 h-6 w-6 md:h-8 md:w-8"
              >
                <X className="h-3 w-3 md:h-4 md:w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-0 flex flex-col h-[calc(500px-64px)] md:h-[calc(600px-80px)]">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-3 md:p-4 space-y-4">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}>
                  <div
                    className={`max-w-[80%] p-2 md:p-3 rounded-lg ${
                      message.type === "user"
                        ? "bg-[#C0322D] text-white"
                        : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white"
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      {message.type === "bot" && <Bot className="h-3 w-3 md:h-4 md:w-4 mt-0.5 flex-shrink-0" />}
                      {message.type === "user" && <User className="h-3 w-3 md:h-4 md:w-4 mt-0.5 flex-shrink-0" />}
                      <div className="text-xs md:text-sm whitespace-pre-line">{message.content}</div>
                    </div>
                    <div className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString("ar-SA", {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </div>
                  </div>
                </div>
              ))}

              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 dark:bg-gray-800 p-2 md:p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Bot className="h-3 w-3 md:h-4 md:w-4" />
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Quick Actions */}
            <div className="p-3 md:p-4 border-t">
              <div className="text-xs text-gray-500 mb-2">إجراءات سريعة:</div>
              <div className="flex flex-wrap gap-1 md:gap-2 mb-3 md:mb-4">
                {quickActions.map((action) => (
                  <Button
                    key={action.id}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAction(action.action)}
                    className="text-xs h-6 md:h-7 bg-transparent"
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Input */}
            <div className="p-3 md:p-4 border-t">
              <div className="flex gap-2">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="اكتب رسالتك هنا..."
                  className="flex-1 text-sm"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isTyping}
                  className="bg-[#C0322D] hover:bg-[#C1352F]"
                  size="sm"
                >
                  <Send className="h-3 w-3 md:h-4 md:w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
