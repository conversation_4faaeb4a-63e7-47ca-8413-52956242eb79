export const demoAccounts = {
  admin: {
    email: "<EMAIL>",
    password: "admin123",
    name: "أحمد المدير",
    role: "admin",
    permissions: ["all"],
  },
  recruiter: {
    email: "<EMAIL>",
    password: "recruiter123",
    name: "فاطمة الموظفة",
    role: "recruiter",
    permissions: ["candidates", "jobs", "interviews"],
  },
  client: {
    email: "<EMAIL>",
    password: "client123",
    name: "خالد العميل",
    role: "client",
    permissions: ["view_jobs", "view_candidates"],
  },
  hr: {
    email: "<EMAIL>",
    password: "hr123",
    name: "سارة الموارد البشرية",
    role: "hr",
    permissions: ["candidates", "reports", "notifications"],
  },
}

export const getDemoAccount = (email: string) => {
  return Object.values(demoAccounts).find((account) => account.email === email)
}
