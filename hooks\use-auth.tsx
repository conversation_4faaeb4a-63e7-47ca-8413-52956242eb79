"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

export type UserRole = "admin" | "hr" | "recruiter" | "client" | "job_seeker"

export interface User {
  id: string
  name: string
  username: string
  email: string
  role: User<PERSON>ole
  avatar?: string
  company?: string
  department?: string
  phone?: string
  permissions: string[]
}

interface AuthContextType {
  user: User | null
  login: (identifier: string, password: string) => Promise<boolean>
  logout: () => void
  isAuthenticated: boolean
  hasPermission: (permission: string) => boolean
  hasRole: (role: UserRole) => boolean
  isRole: (role: UserRole) => boolean
}

// Demo accounts with proper permissions
const demoAccounts = [
  {
    id: "1",
    name: "أحمد المدير",
    username: "admin",
    email: "<EMAIL>",
    password: "admin123",
    role: "admin" as User<PERSON><PERSON>,
    company: "Yas Global Partner",
    department: "الإدارة العامة",
    phone: "+************",
    permissions: ["all"],
  },
  {
    id: "2",
    name: "سارة الموارد البشرية",
    username: "hr",
    email: "<EMAIL>",
    password: "hr123",
    role: "hr" as UserRole,
    company: "Yas Global Partner",
    department: "الموارد البشرية",
    phone: "+************",
    permissions: ["users", "reports", "jobs", "candidates", "notifications", "settings"],
  },
  {
    id: "3",
    name: "محمد المباحث",
    username: "recruiter",
    email: "<EMAIL>",
    password: "recruiter123",
    role: "recruiter" as UserRole,
    company: "Yas Global Partner",
    department: "التوظيف",
    phone: "+************",
    permissions: ["jobs", "candidates", "interviews", "timeline", "notifications"],
  },
  {
    id: "4",
    name: "شركة التقنية المتقدمة",
    username: "client",
    email: "<EMAIL>",
    password: "client123",
    role: "client" as UserRole,
    company: "شركة التقنية المتقدمة",
    department: "العملاء",
    phone: "+966512345678",
    permissions: ["view_jobs", "post_jobs", "view_candidates", "client_portal"],
  },
  {
    id: "5",
    name: "علي الباحث عن عمل",
    username: "jobseeker",
    email: "<EMAIL>",
    password: "jobseeker123",
    role: "job_seeker" as UserRole,
    department: "الباحثين عن عمل",
    phone: "+966515678901",
    permissions: ["view_jobs", "apply_jobs", "profile", "applications"],
  },
]

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    // Check for saved user session
    const savedUser = localStorage.getItem("yas-global-user")
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser))
      } catch (error) {
        console.error("Error loading user session:", error)
        localStorage.removeItem("yas-global-user")
      }
    }
  }, [])

  const login = async (identifier: string, password: string): Promise<boolean> => {
    // Allow login with either username or email
    const account = demoAccounts.find(
      (acc) => (acc.email === identifier || acc.username === identifier) && acc.password === password,
    )

    if (account) {
      const userData: User = {
        id: account.id,
        name: account.name,
        username: account.username,
        email: account.email,
        role: account.role,
        company: account.company,
        department: account.department,
        phone: account.phone,
        permissions: account.permissions,
      }

      setUser(userData)
      localStorage.setItem("yas-global-user", JSON.stringify(userData))
      return true
    }

    return false
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem("yas-global-user")
  }

  const hasPermission = (permission: string): boolean => {
    if (!user) return false
    return user.permissions.includes("all") || user.permissions.includes(permission)
  }

  const hasRole = (role: UserRole): boolean => {
    if (!user) return false
    return user.role === role
  }

  const isRole = (role: UserRole): boolean => {
    return user?.role === role
  }

  const isAuthenticated = !!user

  return (
    <AuthContext.Provider value={{ user, login, logout, isAuthenticated, hasPermission, hasRole, isRole }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
