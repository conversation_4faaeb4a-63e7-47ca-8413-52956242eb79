"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard-layout"
import { HorizontalTimeline } from "@/components/horizontal-timeline"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  User,
  Building,
  Mail,
  Phone,
  Calendar,
  Clock,
  FileText,
  MessageSquare,
  Video,
  CheckCircle,
  XCircle,
  AlertCircle,
  UserCheck,
  Briefcase,
  Award,
  Shield,
  Eye,
  Edit,
  Send,
  Bell,
  Download,
  MapPin,
  GraduationCap,
  Star,
  TrendingUp,
  Users,
  Target,
  Zap,
  Settings,
  Plus,
  Minus,
  MoreHorizontal,
  ThumbsUp,
  ThumbsDown,
  Pause,
  Play,
  RotateCcw,
  Archive,
  Trash2,
  Copy,
  ExternalLink,
  Filter,
  Search,
  SortAsc,
  Calendar as CalendarIcon,
  Clock as ClockIcon
} from "lucide-react"

// بيانات تجريبية للمرشح والوظيفة
const mockApplicationData = {
  1: {
    id: 1,
    candidateName: "أحمد محمد السالم",
    candidateEmail: "<EMAIL>",
    candidatePhone: "+966501234567",
    candidateLocation: "الرياض، المملكة العربية السعودية",
    candidateAge: 28,
    candidateNationality: "سعودي",
    jobTitle: "مهندس بترول أول",
    jobId: "JOB-2024-001",
    company: "أرامكو السعودية",
    department: "قسم الحفر والإنتاج",
    applicationDate: "2024-01-15",
    expectedSalary: "25,000 ريال",
    currentSalary: "20,000 ريال",
    experience: "5 سنوات",
    education: "بكالوريوس هندسة البترول - جامعة الملك فهد",
    gpa: "3.8/4.0",
    graduationYear: "2019",
    languages: ["العربية (أصلي)", "الإنجليزية (ممتاز)", "الفرنسية (جيد)"],
    skills: ["حفر البترول", "تحليل المكامن", "إدارة المشاريع", "AutoCAD", "MATLAB", "Python", "SAP"],
    certifications: ["PMP", "HSE Level 3", "Petroleum Engineering Certificate"],
    previousJobs: [
      { company: "شركة البترول الوطنية", position: "مهندس بترول", duration: "2019-2022" },
      { company: "شلمبرجير", position: "مهندس حفر", duration: "2022-2024" }
    ],
    totalApplications: 3,
    activeApplications: 2,
    completedApplications: 1,
    rating: 4.5,
    notes: "مرشح ممتاز مع خبرة قوية في مجال البترول",
    attachments: [
      { name: "السيرة الذاتية.pdf", size: "2.3 MB", type: "pdf" },
      { name: "الشهادات.pdf", size: "1.8 MB", type: "pdf" },
      { name: "خطاب التوصية.pdf", size: "0.9 MB", type: "pdf" }
    ],
    timeline: [
      {
        id: "application",
        title: "تقديم الطلب",
        description: "تم استلام طلب التوظيف",
        status: "completed",
        date: "2024-01-15",
        time: "09:30",
        automated: true,
        color: "blue",
        icon: FileText
      },
      {
        id: "screening",
        title: "فحص أولي",
        description: "مراجعة السيرة الذاتية والمؤهلات",
        status: "completed",
        date: "2024-01-16",
        time: "14:00",
        automated: false,
        color: "green",
        icon: Eye
      },
      {
        id: "hr_interview",
        title: "مقابلة الموارد البشرية",
        description: "مقابلة أولية مع قسم الموارد البشرية",
        status: "completed",
        date: "2024-01-18",
        time: "10:00",
        automated: false,
        interviewLink: "hr-interview-123",
        color: "purple",
        icon: UserCheck
      },
      {
        id: "technical_test",
        title: "اختبار تقني",
        description: "تقييم المهارات التقنية والمعرفة المتخصصة",
        status: "in_progress",
        date: "2024-01-20",
        time: "15:00",
        automated: false,
        color: "orange",
        icon: Award
      },
      {
        id: "technical_interview",
        title: "مقابلة تقنية",
        description: "مقابلة مع الفريق التقني",
        status: "pending",
        automated: false,
        interviewLink: "tech-interview-456",
        color: "blue",
        icon: Briefcase
      },
      {
        id: "final_interview",
        title: "مقابلة نهائية",
        description: "مقابلة مع الإدارة العليا",
        status: "pending",
        automated: false,
        interviewLink: "final-interview-789",
        color: "red",
        icon: Shield
      },
      {
        id: "decision",
        title: "اتخاذ القرار",
        description: "اتخاذ القرار النهائي بشأن التوظيف",
        status: "pending",
        automated: true,
        color: "green",
        icon: CheckCircle
      }
    ]
  },
  2: {
    id: 2,
    candidateName: "فاطمة علي الزهراني",
    candidateEmail: "<EMAIL>",
    candidatePhone: "+966507654321",
    jobTitle: "محاسبة أولى",
    company: "البنك الأهلي السعودي",
    department: "قسم المحاسبة والمالية",
    applicationDate: "2024-01-10",
    expectedSalary: "18,000 ريال",
    experience: "7 سنوات",
    education: "بكالوريوس المحاسبة - جامعة الملك سعود",
    skills: ["المحاسبة المالية", "التدقيق", "SAP", "Excel المتقدم", "التحليل المالي"],
    timeline: [
      {
        id: "application",
        title: "تقديم الطلب",
        description: "تم استلام طلب التوظيف",
        status: "completed",
        date: "2024-01-10",
        time: "11:15",
        automated: true,
        color: "blue",
        icon: FileText
      },
      {
        id: "screening",
        title: "فحص أولي",
        description: "مراجعة السيرة الذاتية والمؤهلات",
        status: "completed",
        date: "2024-01-12",
        time: "09:00",
        automated: false,
        color: "green",
        icon: Eye
      },
      {
        id: "hr_interview",
        title: "مقابلة الموارد البشرية",
        description: "مقابلة أولية مع قسم الموارد البشرية",
        status: "scheduled",
        date: "2024-01-22",
        time: "13:30",
        automated: false,
        interviewLink: "hr-interview-abc",
        color: "purple",
        icon: UserCheck
      },
      {
        id: "technical_test",
        title: "اختبار تقني",
        description: "تقييم المهارات المحاسبية والمالية",
        status: "pending",
        automated: false,
        color: "orange",
        icon: Award
      },
      {
        id: "final_interview",
        title: "مقابلة نهائية",
        description: "مقابلة مع مدير القسم",
        status: "pending",
        automated: false,
        interviewLink: "final-interview-def",
        color: "red",
        icon: Shield
      },
      {
        id: "decision",
        title: "اتخاذ القرار",
        description: "اتخاذ القرار النهائي بشأن التوظيف",
        status: "pending",
        automated: true,
        color: "green",
        icon: CheckCircle
      }
    ]
  }
}

export default function ApplicationTimelinePage() {
  const params = useParams()
  const applicationId = parseInt(params.id as string)
  const applicationData = mockApplicationData[applicationId as keyof typeof mockApplicationData] || {
    id: applicationId,
    candidateName: "غير محدد",
    candidateEmail: "",
    candidatePhone: "",
    candidateLocation: "",
    candidateAge: 0,
    candidateNationality: "",
    jobTitle: "غير محدد",
    jobId: "",
    company: "",
    department: "",
    applicationDate: "",
    expectedSalary: "",
    currentSalary: "",
    experience: "",
    education: "",
    gpa: "",
    graduationYear: "",
    languages: [],
    skills: [],
    certifications: [],
    previousJobs: [],
    totalApplications: 0,
    activeApplications: 0,
    completedApplications: 0,
    rating: 0,
    notes: "",
    attachments: [],
    timeline: []
  }

  const [timeline, setTimeline] = useState(applicationData?.timeline || [])
  const [notifications, setNotifications] = useState<string[]>([])
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isReminderDialogOpen, setIsReminderDialogOpen] = useState(false)
  const [selectedStage, setSelectedStage] = useState<any>(null)
  const [editData, setEditData] = useState<any>({})
  const [reminderData, setReminderData] = useState({
    type: "",
    subject: "",
    message: "",
    scheduleDate: "",
    scheduleTime: "",
    recipients: []
  })
  const [quickActions, setQuickActions] = useState({
    showActions: false,
    selectedStageId: ""
  })

  if (!applicationData) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">طلب غير موجود</h3>
            <p className="text-gray-600">لم يتم العثور على طلب التوظيف المطلوب</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const handleUpdateStage = (stageId: string, newStatus: string, data?: any) => {
    setTimeline(prev => prev.map(stage =>
      stage.id === stageId
        ? { ...stage, status: newStatus as any, ...data }
        : stage
    ))

    // إضافة إشعار
    const stage = timeline.find(s => s.id === stageId)
    if (stage) {
      const statusText = {
        'in_progress': 'بدأت',
        'completed': 'اكتملت',
        'rejected': 'رُفضت',
        'scheduled': 'جُدولت',
        'approved': 'تم قبولها',
        'on_hold': 'تم تعليقها'
      }[newStatus] || newStatus

      setNotifications(prev => [...prev, `${stage.title} ${statusText} للمرشح ${applicationData.candidateName}`])
    }
  }

  const handleQuickAction = (stageId: string, action: string) => {
    const stage = timeline.find(s => s.id === stageId)
    if (!stage) return

    switch (action) {
      case 'approve':
        handleUpdateStage(stageId, 'completed', {
          notes: 'تم القبول',
          completedAt: new Date().toISOString()
        })
        break
      case 'reject':
        handleUpdateStage(stageId, 'rejected', {
          notes: 'تم الرفض',
          rejectedAt: new Date().toISOString()
        })
        break
      case 'hold':
        handleUpdateStage(stageId, 'on_hold', {
          notes: 'تم التعليق',
          heldAt: new Date().toISOString()
        })
        break
      case 'restart':
        handleUpdateStage(stageId, 'pending', {
          notes: 'تم إعادة التشغيل',
          restartedAt: new Date().toISOString()
        })
        break
    }

    setQuickActions({ showActions: false, selectedStageId: "" })
  }

  const handleEditStage = (stage: any) => {
    setSelectedStage(stage)
    setEditData({
      title: stage.title,
      description: stage.description,
      date: stage.date || "",
      time: stage.time || "",
      notes: stage.notes || "",
      duration: stage.duration || "30"
    })
    setIsEditDialogOpen(true)
  }

  const saveStageEdit = () => {
    if (selectedStage) {
      handleUpdateStage(selectedStage.id, selectedStage.status, editData)
      setIsEditDialogOpen(false)
      setSelectedStage(null)
      setEditData({})
    }
  }

  const handleSendReminder = () => {
    // محاكاة إرسال التذكير
    const reminderText = `تذكير ${reminderData.type}: ${reminderData.subject}`
    setNotifications(prev => [...prev, `تم إرسال ${reminderText} للمرشح ${applicationData.candidateName}`])
    setIsReminderDialogOpen(false)
    setReminderData({
      type: "",
      subject: "",
      message: "",
      scheduleDate: "",
      scheduleTime: "",
      recipients: []
    })
  }

  const handleScheduleInterview = (stageId: string, date: string, time: string) => {
    handleUpdateStage(stageId, "scheduled", { date, time })
  }

  const handleSendNotification = (type: string, message: string) => {
    setNotifications(prev => [...prev, `${type}: ${message}`])
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* رأس الصفحة المحسن */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <User className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{applicationData.candidateName}</h1>
                <p className="text-blue-100">{applicationData.jobTitle} - {applicationData.company}</p>
                <div className="flex items-center gap-4 mt-2 text-sm">
                  <span className="flex items-center gap-1">
                    <Star className="w-4 h-4" />
                    {applicationData.rating}/5
                  </span>
                  <span className="flex items-center gap-1">
                    <Briefcase className="w-4 h-4" />
                    {applicationData.experience}
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {applicationData.candidateLocation}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                onClick={() => setIsReminderDialogOpen(true)}
              >
                <Bell className="w-4 h-4 mr-2" />
                إرسال تذكير
              </Button>
              <Button
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                onClick={() => setIsDetailsDialogOpen(true)}
              >
                <Eye className="w-4 h-4 mr-2" />
                عرض التفاصيل
              </Button>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">إجمالي الطلبات</p>
                <p className="text-xl font-bold">{applicationData.totalApplications}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">طلبات نشطة</p>
                <p className="text-xl font-bold">{applicationData.activeApplications}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">طلبات مكتملة</p>
                <p className="text-xl font-bold">{applicationData.completedApplications}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Clock className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">متوسط الوقت</p>
                <p className="text-xl font-bold">12 يوم</p>
              </div>
            </div>
          </Card>
        </div>

        {/* معلومات المرشح */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl">{applicationData.candidateName}</h2>
                <p className="text-gray-600 font-normal">{applicationData.jobTitle}</p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">معلومات الاتصال</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <span>{applicationData.candidateEmail}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span>{applicationData.candidatePhone}</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">تفاصيل الوظيفة</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Building className="w-4 h-4 text-gray-500" />
                    <span>{applicationData.company}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Briefcase className="w-4 h-4 text-gray-500" />
                    <span>{applicationData.department}</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">الخبرة والتعليم</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span>{applicationData.experience}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4 text-gray-500" />
                    <span>{applicationData.education}</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">المهارات</h4>
                <div className="flex flex-wrap gap-1">
                  {applicationData?.skills?.slice(0, 3).map((skill, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {skill}
                    </Badge>
                  )) || []}
                  {(applicationData?.skills?.length || 0) > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{applicationData.skills.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* التايم لاين التفاعلي المحسن */}
        <Card className="p-6">
          <CardHeader className="px-0 pt-0">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                مراحل التوظيف
              </CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  فلترة
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  تصدير
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-0">
            {/* شريط التقدم */}
            <div className="mb-8">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>التقدم الإجمالي</span>
                <span>{Math.round((timeline.filter(s => s.status === 'completed').length / timeline.length) * 100)}%</span>
              </div>
              <Progress
                value={(timeline.filter(s => s.status === 'completed').length / timeline.length) * 100}
                className="h-2"
              />
            </div>

            {/* التايم لاين التفاعلي */}
            <div className="space-y-4">
              {timeline.map((stage, index) => (
                <div key={stage.id} className="relative">
                  {/* خط الاتصال */}
                  {index < timeline.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"></div>
                  )}

                  <div className="flex items-start gap-4 p-4 rounded-lg border hover:shadow-md transition-all duration-200">
                    {/* أيقونة المرحلة */}
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      stage.status === 'completed' ? 'bg-green-100 text-green-600' :
                      stage.status === 'in_progress' ? 'bg-blue-100 text-blue-600' :
                      stage.status === 'rejected' ? 'bg-red-100 text-red-600' :
                      stage.status === 'scheduled' ? 'bg-orange-100 text-orange-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      <stage.icon className="w-6 h-6" />
                    </div>

                    {/* محتوى المرحلة */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{stage.title}</h3>
                          <p className="text-gray-600 text-sm">{stage.description}</p>
                          {stage.date && (
                            <p className="text-blue-600 text-sm mt-1">
                              {stage.date} {stage.time && `- ${stage.time}`}
                            </p>
                          )}
                        </div>

                        {/* حالة المرحلة */}
                        <div className="flex items-center gap-2">
                          <Badge variant={
                            stage.status === 'completed' ? 'default' :
                            stage.status === 'in_progress' ? 'secondary' :
                            stage.status === 'rejected' ? 'destructive' :
                            stage.status === 'scheduled' ? 'outline' :
                            'outline'
                          }>
                            {stage.status === 'completed' ? 'مكتملة' :
                             stage.status === 'in_progress' ? 'قيد التنفيذ' :
                             stage.status === 'rejected' ? 'مرفوضة' :
                             stage.status === 'scheduled' ? 'مجدولة' :
                             'في الانتظار'}
                          </Badge>
                        </div>
                      </div>

                      {/* أزرار الإجراءات السريعة */}
                      <div className="flex items-center gap-2 mt-3">
                        {stage.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => handleQuickAction(stage.id, 'approve')}
                            >
                              <ThumbsUp className="w-4 h-4 mr-1" />
                              قبول
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleQuickAction(stage.id, 'reject')}
                            >
                              <ThumbsDown className="w-4 h-4 mr-1" />
                              رفض
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleQuickAction(stage.id, 'hold')}
                            >
                              <Pause className="w-4 h-4 mr-1" />
                              تعليق
                            </Button>
                          </>
                        )}

                        {stage.status === 'in_progress' && (
                          <>
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => handleQuickAction(stage.id, 'approve')}
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              إكمال
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleQuickAction(stage.id, 'reject')}
                            >
                              <XCircle className="w-4 h-4 mr-1" />
                              رفض
                            </Button>
                          </>
                        )}

                        {stage.status === 'rejected' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuickAction(stage.id, 'restart')}
                          >
                            <RotateCcw className="w-4 h-4 mr-1" />
                            إعادة تشغيل
                          </Button>
                        )}

                        {stage.title.includes("مقابلة") && stage.status !== 'rejected' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`/dashboard/video-interview?candidate=${applicationData.candidateName}&stage=${stage.id}`, '_blank')}
                          >
                            <Video className="w-4 h-4 mr-1" />
                            بدء المقابلة
                          </Button>
                        )}

                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditStage(stage)}
                        >
                          <Edit className="w-4 h-4 mr-1" />
                          تحرير
                        </Button>

                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            setSelectedStage(stage)
                            setReminderData(prev => ({
                              ...prev,
                              type: "مرحلة " + stage.title,
                              subject: `تذكير بخصوص ${stage.title} للمرشح ${applicationData.candidateName}`
                            }))
                            setIsReminderDialogOpen(true)
                          }}
                        >
                          <Bell className="w-4 h-4 mr-1" />
                          تذكير
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* الإشعارات */}
        {notifications.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5" />
                الإشعارات الأخيرة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {notifications.slice(-5).map((notification, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>{notification}</span>
                    <span className="text-xs text-gray-500 ml-auto">
                      {new Date().toLocaleTimeString('ar-SA')}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* نافذة تفاصيل المرشح */}
        <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                تفاصيل المرشح - {applicationData.candidateName}
              </DialogTitle>
            </DialogHeader>

            <Tabs defaultValue="personal" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="personal">المعلومات الشخصية</TabsTrigger>
                <TabsTrigger value="professional">الخبرة المهنية</TabsTrigger>
                <TabsTrigger value="applications">الطلبات</TabsTrigger>
                <TabsTrigger value="documents">المستندات</TabsTrigger>
              </TabsList>

              <TabsContent value="personal" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>الاسم الكامل</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.candidateName}</p>
                  </div>
                  <div className="space-y-2">
                    <Label>العمر</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.candidateAge} سنة</p>
                  </div>
                  <div className="space-y-2">
                    <Label>الجنسية</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.candidateNationality}</p>
                  </div>
                  <div className="space-y-2">
                    <Label>الموقع</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.candidateLocation}</p>
                  </div>
                  <div className="space-y-2">
                    <Label>البريد الإلكتروني</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.candidateEmail}</p>
                  </div>
                  <div className="space-y-2">
                    <Label>رقم الهاتف</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.candidatePhone}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>التعليم</Label>
                  <p className="p-2 bg-gray-50 rounded">{applicationData.education}</p>
                  <p className="text-sm text-gray-600">المعدل: {applicationData.gpa} - سنة التخرج: {applicationData.graduationYear}</p>
                </div>

                <div className="space-y-2">
                  <Label>اللغات</Label>
                  <div className="flex flex-wrap gap-2">
                    {applicationData?.languages?.map((lang, index) => (
                      <Badge key={index} variant="secondary">{lang}</Badge>
                    )) || <span className="text-gray-500">لا توجد لغات محددة</span>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>الشهادات</Label>
                  <div className="flex flex-wrap gap-2">
                    {applicationData?.certifications?.map((cert, index) => (
                      <Badge key={index} variant="outline">{cert}</Badge>
                    )) || <span className="text-gray-500">لا توجد شهادات محددة</span>}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="professional" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>سنوات الخبرة</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.experience}</p>
                  </div>
                  <div className="space-y-2">
                    <Label>الراتب الحالي</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.currentSalary}</p>
                  </div>
                  <div className="space-y-2">
                    <Label>الراتب المتوقع</Label>
                    <p className="p-2 bg-gray-50 rounded">{applicationData.expectedSalary}</p>
                  </div>
                  <div className="space-y-2">
                    <Label>التقييم</Label>
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[1,2,3,4,5].map(star => (
                          <Star key={star} className={`w-4 h-4 ${star <= (applicationData?.rating || 0) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">{applicationData?.rating || 0}/5</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>المهارات</Label>
                  <div className="flex flex-wrap gap-2">
                    {applicationData?.skills?.map((skill, index) => (
                      <Badge key={index} variant="secondary">{skill}</Badge>
                    )) || <span className="text-gray-500">لا توجد مهارات محددة</span>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>الوظائف السابقة</Label>
                  <div className="space-y-3">
                    {applicationData?.previousJobs?.map((job, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <h4 className="font-semibold">{job.position}</h4>
                        <p className="text-sm text-gray-600">{job.company}</p>
                        <p className="text-xs text-gray-500">{job.duration}</p>
                      </div>
                    )) || <span className="text-gray-500">لا توجد وظائف سابقة محددة</span>}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="applications" className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <Card className="p-4 text-center">
                    <h3 className="text-2xl font-bold text-blue-600">{applicationData.totalApplications}</h3>
                    <p className="text-sm text-gray-600">إجمالي الطلبات</p>
                  </Card>
                  <Card className="p-4 text-center">
                    <h3 className="text-2xl font-bold text-green-600">{applicationData.activeApplications}</h3>
                    <p className="text-sm text-gray-600">طلبات نشطة</p>
                  </Card>
                  <Card className="p-4 text-center">
                    <h3 className="text-2xl font-bold text-purple-600">{applicationData.completedApplications}</h3>
                    <p className="text-sm text-gray-600">طلبات مكتملة</p>
                  </Card>
                </div>

                <div className="space-y-2">
                  <Label>الوظيفة الحالية</Label>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold">{applicationData.jobTitle}</h4>
                        <p className="text-sm text-gray-600">{applicationData.company} - {applicationData.department}</p>
                        <p className="text-xs text-gray-500">رقم الوظيفة: {applicationData.jobId}</p>
                      </div>
                      <Badge variant="outline">نشط</Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>ملاحظات</Label>
                  <p className="p-3 bg-gray-50 rounded">{applicationData.notes}</p>
                </div>
              </TabsContent>

              <TabsContent value="documents" className="space-y-4">
                <div className="space-y-3">
                  {applicationData?.attachments?.map((doc, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="w-8 h-8 text-blue-600" />
                        <div>
                          <p className="font-medium">{doc.name}</p>
                          <p className="text-sm text-gray-600">{doc.size} - {doc.type.toUpperCase()}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="w-4 h-4 mr-1" />
                          عرض
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="w-4 h-4 mr-1" />
                          تحميل
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>

        {/* نافذة تحرير المرحلة */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تحرير المرحلة - {selectedStage?.title}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>عنوان المرحلة</Label>
                <Input
                  value={editData.title || ""}
                  onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label>الوصف</Label>
                <Textarea
                  value={editData.description || ""}
                  onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>التاريخ</Label>
                  <Input
                    type="date"
                    value={editData.date || ""}
                    onChange={(e) => setEditData(prev => ({ ...prev, date: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>الوقت</Label>
                  <Input
                    type="time"
                    value={editData.time || ""}
                    onChange={(e) => setEditData(prev => ({ ...prev, time: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>المدة (بالدقائق)</Label>
                <Select value={editData.duration || "30"} onValueChange={(value) => setEditData(prev => ({ ...prev, duration: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 دقيقة</SelectItem>
                    <SelectItem value="30">30 دقيقة</SelectItem>
                    <SelectItem value="45">45 دقيقة</SelectItem>
                    <SelectItem value="60">60 دقيقة</SelectItem>
                    <SelectItem value="90">90 دقيقة</SelectItem>
                    <SelectItem value="120">120 دقيقة</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>ملاحظات</Label>
                <Textarea
                  value={editData.notes || ""}
                  onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={2}
                  placeholder="أضف ملاحظات إضافية..."
                />
              </div>

              <div className="flex gap-2 pt-4">
                <Button onClick={saveStageEdit} className="flex-1">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  حفظ التغييرات
                </Button>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* نافذة إرسال التذكير */}
        <Dialog open={isReminderDialogOpen} onOpenChange={setIsReminderDialogOpen}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5" />
                إرسال تذكير
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>نوع التذكير</Label>
                <Select value={reminderData.type} onValueChange={(value) => setReminderData(prev => ({ ...prev, type: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع التذكير" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="email">بريد إلكتروني</SelectItem>
                    <SelectItem value="sms">رسالة نصية</SelectItem>
                    <SelectItem value="notification">إشعار في النظام</SelectItem>
                    <SelectItem value="call">مكالمة هاتفية</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>موضوع التذكير</Label>
                <Input
                  value={reminderData.subject}
                  onChange={(e) => setReminderData(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="أدخل موضوع التذكير"
                />
              </div>

              <div className="space-y-2">
                <Label>محتوى الرسالة</Label>
                <Textarea
                  value={reminderData.message}
                  onChange={(e) => setReminderData(prev => ({ ...prev, message: e.target.value }))}
                  rows={4}
                  placeholder="اكتب محتوى التذكير هنا..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>تاريخ الإرسال</Label>
                  <Input
                    type="date"
                    value={reminderData.scheduleDate}
                    onChange={(e) => setReminderData(prev => ({ ...prev, scheduleDate: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>وقت الإرسال</Label>
                  <Input
                    type="time"
                    value={reminderData.scheduleTime}
                    onChange={(e) => setReminderData(prev => ({ ...prev, scheduleTime: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>المستقبلون</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="candidate"
                      className="rounded"
                      defaultChecked
                    />
                    <label htmlFor="candidate" className="text-sm">
                      المرشح ({applicationData.candidateName})
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="hr"
                      className="rounded"
                    />
                    <label htmlFor="hr" className="text-sm">
                      قسم الموارد البشرية
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="manager"
                      className="rounded"
                    />
                    <label htmlFor="manager" className="text-sm">
                      المدير المباشر
                    </label>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="bg-blue-50 p-3 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">معاينة التذكير</h4>
                <div className="text-sm text-blue-800">
                  <p><strong>النوع:</strong> {reminderData.type || "غير محدد"}</p>
                  <p><strong>الموضوع:</strong> {reminderData.subject || "غير محدد"}</p>
                  <p><strong>التوقيت:</strong> {reminderData.scheduleDate && reminderData.scheduleTime ?
                    `${reminderData.scheduleDate} في ${reminderData.scheduleTime}` : "فوري"}</p>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={handleSendReminder}
                  className="flex-1"
                  disabled={!reminderData.type || !reminderData.subject}
                >
                  <Send className="w-4 h-4 mr-2" />
                  إرسال التذكير
                </Button>
                <Button variant="outline" onClick={() => setIsReminderDialogOpen(false)}>
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
