"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Calendar,
  User,
  Building,
  Phone,
  Mail,
  MessageSquare,
  Edit,
  Plus,
  Eye,
  FileText,
  Send,
  Video,
  PlayCircle,
  StopCircle,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  Users,
  Bell,
  CheckSquare,
  X,
  UserCheck,
  Briefcase,
  Award,
  Shield,
  Pause,
  RotateCcw
} from "lucide-react"

interface TimelineStage {
  id: string
  title: string
  description: string
  status: "pending" | "in_progress" | "completed" | "rejected" | "scheduled"
  date?: string
  time?: string
  notes?: string
  automated?: boolean
  interviewLink?: string
  color: string
  icon: any
}

interface HorizontalTimelineProps {
  applicationId: number
  candidateName: string
  jobTitle: string
  company: string
  timeline: TimelineStage[]
  onUpdateStage: (stageId: string, newStatus: string, data?: any) => void
  onScheduleInterview: (stageId: string, date: string, time: string) => void
  onSendNotification: (type: string, message: string) => void
}

export function HorizontalTimeline({
  applicationId,
  candidateName,
  jobTitle,
  company,
  timeline,
  onUpdateStage,
  onScheduleInterview,
  onSendNotification
}: HorizontalTimelineProps) {
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [selectedStageId, setSelectedStageId] = useState<string>("")
  const [scheduleDate, setScheduleDate] = useState("")
  const [scheduleTime, setScheduleTime] = useState("")
  const [scheduleNotes, setScheduleNotes] = useState("")

  const getStageIcon = (stage: TimelineStage) => {
    const IconComponent = stage.icon
    const baseClasses = "w-6 h-6"
    
    switch (stage.status) {
      case "completed":
        return <CheckCircle className={`${baseClasses} text-white`} />
      case "in_progress":
        return <Clock className={`${baseClasses} text-white`} />
      case "scheduled":
        return <Calendar className={`${baseClasses} text-white`} />
      case "rejected":
        return <XCircle className={`${baseClasses} text-white`} />
      default:
        return <IconComponent className={`${baseClasses} text-gray-400`} />
    }
  }

  const getStageColor = (stage: TimelineStage) => {
    switch (stage.status) {
      case "completed":
        return "bg-green-500"
      case "in_progress":
        return "bg-blue-500"
      case "scheduled":
        return "bg-orange-500"
      case "rejected":
        return "bg-red-500"
      default:
        return "bg-gray-300"
    }
  }

  const getConnectorColor = (currentStage: TimelineStage, nextStage?: TimelineStage) => {
    if (currentStage.status === "completed") {
      return "bg-green-500"
    } else if (currentStage.status === "rejected") {
      return "bg-red-500"
    } else if (currentStage.status === "in_progress") {
      return "bg-blue-500"
    }
    return "bg-gray-300"
  }

  const handleRejectStage = (stageId: string) => {
    const stageIndex = timeline.findIndex(stage => stage.id === stageId)
    
    // رفض المرحلة الحالية
    onUpdateStage(stageId, "rejected")
    
    // إخفاء/رفض جميع المراحل التالية
    for (let i = stageIndex + 1; i < timeline.length; i++) {
      onUpdateStage(timeline[i].id, "rejected")
    }
  }

  const handleScheduleInterview = () => {
    if (selectedStageId && scheduleDate && scheduleTime) {
      onScheduleInterview(selectedStageId, scheduleDate, scheduleTime)
      setIsScheduleDialogOpen(false)
      setScheduleDate("")
      setScheduleTime("")
      setScheduleNotes("")
      setSelectedStageId("")
    }
  }

  const openScheduleDialog = (stageId: string) => {
    setSelectedStageId(stageId)
    setIsScheduleDialogOpen(true)
  }

  // فلترة المراحل المرئية (إخفاء المراحل بعد الرفض)
  const getVisibleStages = () => {
    const rejectedIndex = timeline.findIndex(stage => stage.status === "rejected")
    if (rejectedIndex !== -1) {
      return timeline.slice(0, rejectedIndex + 1)
    }
    return timeline
  }

  const visibleStages = getVisibleStages()

  // حساب نسبة التقدم
  const progressPercentage = (timeline.filter(s => s.status === 'completed').length / timeline.length) * 100

  return (
    <div className="w-full p-6 bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-gray-200">
      {/* رأس التايم لاين */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
              <User className="w-7 h-7 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">{candidateName}</h3>
              <p className="text-gray-600 text-lg">{jobTitle} - {company}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">التقدم الإجمالي</div>
            <div className="text-2xl font-bold text-blue-600">{Math.round(progressPercentage)}%</div>
          </div>
        </div>

        {/* شريط التقدم */}
        <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
          <div
            className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* التايم لاين الأفقي المحسن */}
      <div className="relative overflow-x-auto">
        <div className="flex items-start justify-between min-w-full pb-8" style={{ minWidth: `${visibleStages.length * 200}px` }}>
          {visibleStages.map((stage, index) => (
            <div key={stage.id} className="flex flex-col items-center relative flex-1 min-w-0">
              {/* الخط الواصل */}
              {index < visibleStages.length - 1 && (
                <div className="absolute top-10 left-1/2 w-full h-1 z-0 flex items-center">
                  <div className={`h-1 flex-1 ${getConnectorColor(stage, visibleStages[index + 1])}`}></div>
                </div>
              )}

              {/* الدائرة والأيقونة */}
              <div className={`w-20 h-20 rounded-full ${getStageColor(stage)} flex items-center justify-center shadow-xl relative z-10 transition-all duration-300 hover:scale-110`}>
                {getStageIcon(stage)}

                {/* رقم المرحلة */}
                <div className="absolute -top-2 -right-2 w-7 h-7 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center text-xs font-bold text-gray-700 z-20 shadow-md">
                  {String(index + 1).padStart(2, '0')}
                </div>
              </div>

              {/* محتوى المرحلة */}
              <div className="mt-6 text-center max-w-40 px-2">
                <h4 className="font-bold text-base text-gray-900 mb-2">{stage.title}</h4>
                <p className="text-sm text-gray-600 leading-relaxed mb-3">{stage.description}</p>

                {/* معلومات إضافية */}
                {stage.date && (
                  <div className="bg-blue-50 rounded-lg p-2 mb-3">
                    <p className="text-xs text-blue-700 font-medium">
                      📅 {stage.date}
                      {stage.time && ` ⏰ ${stage.time}`}
                    </p>
                  </div>
                )}

                {/* حالة المرحلة */}
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  stage.status === 'completed' ? 'bg-green-100 text-green-800' :
                  stage.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                  stage.status === 'rejected' ? 'bg-red-100 text-red-800' :
                  stage.status === 'scheduled' ? 'bg-orange-100 text-orange-800' :
                  'bg-gray-100 text-gray-600'
                }`}>
                  {stage.status === 'completed' ? '✅ مكتملة' :
                   stage.status === 'in_progress' ? '🔄 قيد التنفيذ' :
                   stage.status === 'rejected' ? '❌ مرفوضة' :
                   stage.status === 'scheduled' ? '📅 مجدولة' :
                   '⏳ في الانتظار'}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* أزرار التحكم المحسنة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-8">
          {visibleStages.map((stage, index) => (
            <div key={`controls-${stage.id}`} className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="flex items-center gap-3 mb-3">
                <div className={`w-8 h-8 rounded-full ${getStageColor(stage)} flex items-center justify-center`}>
                  <stage.icon className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h5 className="font-semibold text-sm text-gray-900">{stage.title}</h5>
                  <p className="text-xs text-gray-500">المرحلة {index + 1}</p>
                </div>
              </div>

              <div className="space-y-2">
                {stage.status === "pending" && (
                  <>
                    <Button
                      size="sm"
                      className="w-full bg-green-600 hover:bg-green-700"
                      onClick={() => onUpdateStage(stage.id, "in_progress")}
                    >
                      <CheckSquare className="w-4 h-4 mr-2" />
                      بدء المرحلة
                    </Button>
                    {stage.title.includes("مقابلة") && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full"
                        onClick={() => openScheduleDialog(stage.id)}
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        جدولة موعد
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="destructive"
                      className="w-full"
                      onClick={() => handleRejectStage(stage.id)}
                    >
                      <X className="w-4 h-4 mr-2" />
                      رفض المرحلة
                    </Button>
                  </>
                )}

                {stage.status === "in_progress" && (
                  <>
                    <Button
                      size="sm"
                      className="w-full bg-green-600 hover:bg-green-700"
                      onClick={() => onUpdateStage(stage.id, "completed")}
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      إكمال المرحلة
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      className="w-full"
                      onClick={() => handleRejectStage(stage.id)}
                    >
                      <X className="w-4 h-4 mr-2" />
                      رفض المرحلة
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full"
                      onClick={() => onUpdateStage(stage.id, "pending")}
                    >
                      <Pause className="w-4 h-4 mr-2" />
                      إيقاف مؤقت
                    </Button>
                  </>
                )}

                {stage.status === "scheduled" && (
                  <>
                    {stage.interviewLink && (
                      <Button
                        size="sm"
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        onClick={() => window.open(`/dashboard/video-interview?link=${stage.interviewLink}&candidate=${candidateName}`, '_blank')}
                      >
                        <Video className="w-4 h-4 mr-2" />
                        بدء المقابلة
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full"
                      onClick={() => openScheduleDialog(stage.id)}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      تعديل الموعد
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      className="w-full"
                      onClick={() => handleRejectStage(stage.id)}
                    >
                      <X className="w-4 h-4 mr-2" />
                      إلغاء المقابلة
                    </Button>
                  </>
                )}

                {stage.status === "completed" && (
                  <div className="text-center py-2">
                    <div className="text-green-600 font-semibold text-sm">✅ تم الإكمال</div>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="w-full mt-2"
                      onClick={() => onUpdateStage(stage.id, "in_progress")}
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      إعادة فتح
                    </Button>
                  </div>
                )}

                {stage.status === "rejected" && (
                  <div className="text-center py-2">
                    <div className="text-red-600 font-semibold text-sm">❌ مرفوضة</div>
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full mt-2"
                      onClick={() => onUpdateStage(stage.id, "pending")}
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      إعادة تشغيل
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* حالة التايم لاين */}
        <div className="mt-6 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-50 rounded-full">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600">
              المرحلة الحالية: {visibleStages.find(s => s.status === "in_progress")?.title || "في الانتظار"}
            </span>
          </div>
        </div>
      </div>

      {/* Dialog جدولة المقابلة */}
      <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>جدولة مقابلة - {candidateName}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>المرشح:</strong> {candidateName}<br/>
                <strong>الوظيفة:</strong> {jobTitle}<br/>
                <strong>الشركة:</strong> {company}
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>التاريخ</Label>
                <Input 
                  type="date" 
                  value={scheduleDate}
                  onChange={(e) => setScheduleDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label>الوقت</Label>
                <Input 
                  type="time" 
                  value={scheduleTime}
                  onChange={(e) => setScheduleTime(e.target.value)}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>ملاحظات إضافية</Label>
              <Textarea 
                placeholder="ملاحظات حول المقابلة..."
                rows={2}
                value={scheduleNotes}
                onChange={(e) => setScheduleNotes(e.target.value)}
              />
            </div>
            
            <div className="flex gap-2 pt-4">
              <Button 
                className="bg-red-600 hover:bg-red-700 flex-1"
                onClick={handleScheduleInterview}
                disabled={!scheduleDate || !scheduleTime}
              >
                <Calendar className="w-4 h-4 mr-2" />
                جدولة المقابلة
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setIsScheduleDialogOpen(false)}
              >
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
