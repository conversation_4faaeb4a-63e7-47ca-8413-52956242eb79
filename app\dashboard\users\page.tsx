"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Users, Plus, Search, Edit, Trash2, Eye, Shield, Mail, Phone, Download } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { useAuth } from "@/hooks/use-auth"
import { useLanguage } from "@/hooks/use-language"

interface User {
  id: string
  name: string
  username: string
  email: string
  password: string
  phone?: string
  role: "admin" | "hr" | "recruiter" | "client" | "job_seeker"
  status: "active" | "inactive" | "pending"
  company?: string
  department?: string
  joinDate: string
  lastLogin?: string
  permissions: string[]
}

const mockUsers: User[] = [
  {
    id: "1",
    name: "أحمد محمد",
    username: "admin",
    email: "<EMAIL>",
    password: "admin123",
    phone: "+966501234567",
    role: "admin",
    status: "active",
    department: "الإدارة",
    joinDate: "2023-01-15",
    lastLogin: "2024-01-20",
    permissions: ["all"],
  },
  {
    id: "2",
    name: "فاطمة علي",
    username: "hr",
    email: "<EMAIL>",
    password: "hr123",
    phone: "+966507654321",
    role: "hr",
    status: "active",
    department: "الموارد البشرية",
    joinDate: "2023-03-10",
    lastLogin: "2024-01-19",
    permissions: ["users", "reports", "jobs", "candidates"],
  },
  {
    id: "3",
    name: "محمد السعد",
    username: "recruiter",
    email: "<EMAIL>",
    password: "recruiter123",
    phone: "+966509876543",
    role: "recruiter",
    status: "active",
    department: "التوظيف",
    joinDate: "2023-06-20",
    lastLogin: "2024-01-18",
    permissions: ["jobs", "candidates"],
  },
  {
    id: "4",
    name: "شركة المستقبل",
    username: "client",
    email: "<EMAIL>",
    password: "client123",
    phone: "+966512345678",
    role: "client",
    status: "active",
    company: "شركة المستقبل",
    joinDate: "2023-09-05",
    lastLogin: "2024-01-17",
    permissions: ["view_jobs", "post_jobs"],
  },
  {
    id: "5",
    name: "علي الباحث عن عمل",
    username: "jobseeker",
    email: "<EMAIL>",
    password: "jobseeker123",
    phone: "+966515678901",
    role: "job_seeker",
    status: "active",
    joinDate: "2023-11-12",
    lastLogin: "2024-01-16",
    permissions: ["view_jobs", "apply_jobs", "profile"],
  },
]

const roleLabels = {
  admin: "مدير النظام",
  hr: "موارد بشرية",
  recruiter: "مسؤول توظيف",
  client: "عميل",
  job_seeker: "باحث عن عمل",
}

const statusLabels = {
  active: "نشط",
  inactive: "غير نشط",
  pending: "معلق",
}

export default function UsersPage() {
  const { user, hasPermission } = useAuth()
  const { t } = useLanguage()
  const [users, setUsers] = useState<User[]>(mockUsers)
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [viewingUser, setViewingUser] = useState<User | null>(null)
  const [newUser, setNewUser] = useState({
    name: "",
    username: "",
    email: "",
    password: "",
    phone: "",
    role: "job_seeker" as User["role"],
    status: "active" as User["status"],
    company: "",
    department: "",
    permissions: [] as string[],
  })

  // Load users from localStorage
  useEffect(() => {
    const savedUsers = localStorage.getItem("yas-global-users")
    if (savedUsers) {
      try {
        setUsers(JSON.parse(savedUsers))
      } catch (error) {
        console.error("Error loading users:", error)
      }
    }
  }, [])

  // Save users to localStorage
  const saveUsers = (newUsers: User[]) => {
    setUsers(newUsers)
    localStorage.setItem("yas-global-users", JSON.stringify(newUsers))
  }

  // Check permissions
  if (!hasPermission("users")) {
    return (
      <DashboardLayout>
        <div className="p-6 text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">غير مصرح</h2>
          <p className="text-gray-600">ليس لديك صلاحية لإدارة المستخدمين</p>
        </div>
      </DashboardLayout>
    )
  }

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = roleFilter === "all" || user.role === roleFilter
    const matchesStatus = statusFilter === "all" || user.status === statusFilter

    return matchesSearch && matchesRole && matchesStatus
  })

  const handleAddUser = () => {
    const user: User = {
      id: Date.now().toString(),
      ...newUser,
      joinDate: new Date().toISOString().split("T")[0],
      permissions: getDefaultPermissions(newUser.role),
    }

    saveUsers([...users, user])
    setIsAddDialogOpen(false)
    resetForm()
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setNewUser({
      name: user.name,
      username: user.username,
      email: user.email,
      password: user.password,
      phone: user.phone || "",
      role: user.role,
      status: user.status,
      company: user.company || "",
      department: user.department || "",
      permissions: user.permissions,
    })
  }

  const handleUpdateUser = () => {
    if (!editingUser) return

    const updatedUsers = users.map((u) =>
      u.id === editingUser.id ? { ...u, ...newUser, permissions: getDefaultPermissions(newUser.role) } : u,
    )

    saveUsers(updatedUsers)
    setEditingUser(null)
    resetForm()
  }

  const handleDeleteUser = (userId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا المستخدم؟")) {
      saveUsers(users.filter((u) => u.id !== userId))
    }
  }

  const resetForm = () => {
    setNewUser({
      name: "",
      username: "",
      email: "",
      password: "",
      phone: "",
      role: "job_seeker",
      status: "active",
      company: "",
      department: "",
      permissions: [],
    })
  }

  const getDefaultPermissions = (role: User["role"]): string[] => {
    switch (role) {
      case "admin":
        return ["all"]
      case "hr":
        return ["users", "reports", "jobs", "candidates", "notifications", "settings"]
      case "recruiter":
        return ["jobs", "candidates", "notifications"]
      case "client":
        return ["view_jobs", "post_jobs", "client_portal"]
      case "job_seeker":
        return ["view_jobs", "apply_jobs", "profile", "applications"]
      default:
        return []
    }
  }

  const getRoleColor = (role: User["role"]) => {
    switch (role) {
      case "admin":
        return "bg-red-100 text-red-800"
      case "hr":
        return "bg-blue-100 text-blue-800"
      case "recruiter":
        return "bg-green-100 text-green-800"
      case "client":
        return "bg-purple-100 text-purple-800"
      case "job_seeker":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusColor = (status: User["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "inactive":
        return "bg-red-100 text-red-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const exportToExcel = () => {
    const exportData = filteredUsers.map((user) => ({
      الاسم: user.name,
      "اسم المستخدم": user.username,
      "البريد الإلكتروني": user.email,
      "كلمة المرور": user.password,
      "رقم الهاتف": user.phone || "",
      الدور: roleLabels[user.role],
      الحالة: statusLabels[user.status],
      الشركة: user.company || "",
      القسم: user.department || "",
      "تاريخ الانضمام": user.joinDate,
      "آخر تسجيل دخول": user.lastLogin || "لم يسجل دخول",
    }))

    // Create CSV content
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(","),
      ...exportData.map((row) => headers.map((header) => `"${row[header as keyof typeof row] || ""}"`).join(",")),
    ].join("\n")

    // Add BOM for proper Arabic display in Excel
    const BOM = "\uFEFF"
    const blob = new Blob([BOM + csvContent], { type: "text/csv;charset=utf-8;" })

    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `تقرير_المستخدمين_${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Users className="h-6 w-6" />
              إدارة المستخدمين
            </h1>
            <p className="text-gray-600 mt-1">إدارة حسابات المستخدمين والصلاحيات</p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToExcel} className="bg-transparent">
              <Download className="h-4 w-4 ml-2" />
              تصدير Excel
            </Button>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#C0322D] hover:bg-[#C1352F]">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة مستخدم جديد
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>إضافة مستخدم جديد</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">الاسم</Label>
                    <Input
                      id="name"
                      value={newUser.name}
                      onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                      placeholder="أدخل الاسم"
                    />
                  </div>

                  <div>
                    <Label htmlFor="username">اسم المستخدم</Label>
                    <Input
                      id="username"
                      value={newUser.username}
                      onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                      placeholder="أدخل اسم المستخدم"
                    />
                  </div>

                  <div>
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                      placeholder="أدخل البريد الإلكتروني"
                    />
                  </div>

                  <div>
                    <Label htmlFor="password">كلمة المرور</Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                      placeholder="أدخل كلمة المرور"
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      value={newUser.phone}
                      onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                      placeholder="أدخل رقم الهاتف"
                    />
                  </div>

                  <div>
                    <Label htmlFor="role">الدور</Label>
                    <Select
                      value={newUser.role}
                      onValueChange={(value: User["role"]) => setNewUser({ ...newUser, role: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">مدير النظام</SelectItem>
                        <SelectItem value="hr">موارد بشرية</SelectItem>
                        <SelectItem value="recruiter">مسؤول توظيف</SelectItem>
                        <SelectItem value="client">عميل</SelectItem>
                        <SelectItem value="job_seeker">باحث عن عمل</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {newUser.role === "client" && (
                    <div>
                      <Label htmlFor="company">الشركة</Label>
                      <Input
                        id="company"
                        value={newUser.company}
                        onChange={(e) => setNewUser({ ...newUser, company: e.target.value })}
                        placeholder="أدخل اسم الشركة"
                      />
                    </div>
                  )}

                  {(newUser.role === "admin" || newUser.role === "hr" || newUser.role === "recruiter") && (
                    <div>
                      <Label htmlFor="department">القسم</Label>
                      <Input
                        id="department"
                        value={newUser.department}
                        onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}
                        placeholder="أدخل القسم"
                      />
                    </div>
                  )}

                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddUser} className="flex-1">
                      إضافة
                    </Button>
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} className="flex-1">
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث بالاسم أو البريد الإلكتروني أو اسم المستخدم..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="تصفية حسب الدور" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأدوار</SelectItem>
                  <SelectItem value="admin">مدير النظام</SelectItem>
                  <SelectItem value="hr">موارد بشرية</SelectItem>
                  <SelectItem value="recruiter">مسؤول توظيف</SelectItem>
                  <SelectItem value="client">عميل</SelectItem>
                  <SelectItem value="job_seeker">باحث عن عمل</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="تصفية حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="inactive">غير نشط</SelectItem>
                  <SelectItem value="pending">معلق</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Users Table - Desktop */}
        <Card className="hidden md:block">
          <CardHeader>
            <CardTitle>المستخدمين ({filteredUsers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المستخدم</TableHead>
                  <TableHead>الدور</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>آخر تسجيل دخول</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500 space-y-1">
                          <div className="flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {user.email}
                          </div>
                          <div>@{user.username}</div>
                          {user.phone && (
                            <div className="flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {user.phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRoleColor(user.role)}>{roleLabels[user.role]}</Badge>
                      {user.company && <div className="text-xs text-gray-500 mt-1">{user.company}</div>}
                      {user.department && <div className="text-xs text-gray-500">{user.department}</div>}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(user.status)}>{statusLabels[user.status]}</Badge>
                    </TableCell>
                    <TableCell>
                      {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString("ar-SA") : "لم يسجل دخول"}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" onClick={() => setViewingUser(user)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleEditUser(user)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Users Cards - Mobile */}
        <div className="md:hidden space-y-4">
          {filteredUsers.map((user) => (
            <Card key={user.id}>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{user.name}</h3>
                      <p className="text-sm text-gray-600">@{user.username}</p>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      {user.phone && <p className="text-sm text-gray-600">{user.phone}</p>}
                    </div>
                    <div className="flex flex-col gap-1">
                      <Badge className={getRoleColor(user.role)}>{roleLabels[user.role]}</Badge>
                      <Badge className={getStatusColor(user.status)}>{statusLabels[user.status]}</Badge>
                    </div>
                  </div>

                  {(user.company || user.department) && (
                    <div className="text-sm text-gray-500">
                      {user.company && <div>الشركة: {user.company}</div>}
                      {user.department && <div>القسم: {user.department}</div>}
                    </div>
                  )}

                  <div className="text-sm text-gray-500">
                    <div>تاريخ الانضمام: {new Date(user.joinDate).toLocaleDateString("ar-SA")}</div>
                    <div>
                      آخر تسجيل دخول:{" "}
                      {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString("ar-SA") : "لم يسجل دخول"}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 bg-transparent"
                      onClick={() => setViewingUser(user)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      عرض
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleEditUser(user)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteUser(user.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">لا توجد نتائج</h3>
              <p className="text-gray-600">لم يتم العثور على مستخدمين مطابقين للبحث</p>
            </CardContent>
          </Card>
        )}

        {/* View User Dialog */}
        <Dialog open={!!viewingUser} onOpenChange={() => setViewingUser(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>تفاصيل المستخدم</DialogTitle>
            </DialogHeader>
            {viewingUser && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">الاسم</Label>
                    <p className="text-lg">{viewingUser.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">اسم المستخدم</Label>
                    <p className="text-lg">@{viewingUser.username}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">البريد الإلكتروني</Label>
                    <p className="text-lg">{viewingUser.email}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">رقم الهاتف</Label>
                    <p className="text-lg">{viewingUser.phone || "غير محدد"}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">الدور</Label>
                    <Badge className={getRoleColor(viewingUser.role)}>{roleLabels[viewingUser.role]}</Badge>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">الحالة</Label>
                    <Badge className={getStatusColor(viewingUser.status)}>{statusLabels[viewingUser.status]}</Badge>
                  </div>
                  {viewingUser.company && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">الشركة</Label>
                      <p className="text-lg">{viewingUser.company}</p>
                    </div>
                  )}
                  {viewingUser.department && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">القسم</Label>
                      <p className="text-lg">{viewingUser.department}</p>
                    </div>
                  )}
                  <div>
                    <Label className="text-sm font-medium text-gray-500">تاريخ الانضمام</Label>
                    <p className="text-lg">{new Date(viewingUser.joinDate).toLocaleDateString("ar-SA")}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">آخر تسجيل دخول</Label>
                    <p className="text-lg">
                      {viewingUser.lastLogin
                        ? new Date(viewingUser.lastLogin).toLocaleDateString("ar-SA")
                        : "لم يسجل دخول"}
                    </p>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-500">الصلاحيات</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {viewingUser.permissions.map((permission) => (
                      <Badge key={permission} variant="outline">
                        {permission}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Edit Dialog */}
        <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل المستخدم</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">الاسم</Label>
                <Input
                  id="edit-name"
                  value={newUser.name}
                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-username">اسم المستخدم</Label>
                <Input
                  id="edit-username"
                  value={newUser.username}
                  onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-email">البريد الإلكتروني</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-password">كلمة المرور</Label>
                <Input
                  id="edit-password"
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-phone">رقم الهاتف</Label>
                <Input
                  id="edit-phone"
                  value={newUser.phone}
                  onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-role">الدور</Label>
                <Select
                  value={newUser.role}
                  onValueChange={(value: User["role"]) => setNewUser({ ...newUser, role: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">مدير النظام</SelectItem>
                    <SelectItem value="hr">موارد بشرية</SelectItem>
                    <SelectItem value="recruiter">مسؤول توظيف</SelectItem>
                    <SelectItem value="client">عميل</SelectItem>
                    <SelectItem value="job_seeker">باحث عن عمل</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="edit-status">الحالة</Label>
                <Select
                  value={newUser.status}
                  onValueChange={(value: User["status"]) => setNewUser({ ...newUser, status: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="inactive">غير نشط</SelectItem>
                    <SelectItem value="pending">معلق</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {newUser.role === "client" && (
                <div>
                  <Label htmlFor="edit-company">الشركة</Label>
                  <Input
                    id="edit-company"
                    value={newUser.company}
                    onChange={(e) => setNewUser({ ...newUser, company: e.target.value })}
                  />
                </div>
              )}

              {(newUser.role === "admin" || newUser.role === "hr" || newUser.role === "recruiter") && (
                <div>
                  <Label htmlFor="edit-department">القسم</Label>
                  <Input
                    id="edit-department"
                    value={newUser.department}
                    onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}
                  />
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button onClick={handleUpdateUser} className="flex-1">
                  حفظ التغييرات
                </Button>
                <Button variant="outline" onClick={() => setEditingUser(null)} className="flex-1">
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
