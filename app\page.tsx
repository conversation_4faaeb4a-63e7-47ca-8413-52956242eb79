import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON>S<PERSON><PERSON> } from "@/components/hero-section"
import { ServicesSection } from "@/components/services-section"
import { AboutSection } from "@/components/about-section"
import { ContactSection } from "@/components/contact-section"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { AIChatbot } from "@/components/ai-chatbot"

export const metadata: Metadata = {
  title: "Yas Global Partner - Transforming HR with Excellence",
  description:
    "Leading talent solutions and HR consulting company providing comprehensive recruitment and human resources services.",
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <HeroSection />
        <AboutSection />
        <ServicesSection />
        <ContactSection />
      </main>
      <Footer />
      <AIChatbot />
    </div>
  )
}
