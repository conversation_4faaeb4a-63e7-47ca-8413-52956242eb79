"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Copy, User, Shield, Briefcase, Users } from "lucide-react"
import { demoAccounts } from "@/data/demo-accounts"

export function DemoAccountsInfo() {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return <Shield className="h-4 w-4" />
      case "recruiter":
        return <User className="h-4 w-4" />
      case "client":
        return <Briefcase className="h-4 w-4" />
      case "hr":
        return <Users className="h-4 w-4" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-red-100 text-red-800"
      case "recruiter":
        return "bg-blue-100 text-blue-800"
      case "client":
        return "bg-green-100 text-green-800"
      case "hr":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getRoleName = (role: string) => {
    switch (role) {
      case "admin":
        return "مدير النظام"
      case "recruiter":
        return "موظف التوظيف"
      case "client":
        return "عميل"
      case "hr":
        return "موارد بشرية"
      default:
        return role
    }
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          حسابات تجريبية للاختبار
        </CardTitle>
        <CardDescription>يمكنك استخدام هذه الحسابات التجريبية لاختبار النظام بأدوار مختلفة</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-2 gap-4">
          {Object.entries(demoAccounts).map(([key, account]) => (
            <div key={key} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getRoleIcon(account.role)}
                  <span className="font-medium">{account.name}</span>
                </div>
                <Badge className={getRoleColor(account.role)}>{getRoleName(account.role)}</Badge>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">البريد الإلكتروني:</span>
                  <div className="flex items-center gap-2">
                    <code className="bg-gray-100 px-2 py-1 rounded text-xs">{account.email}</code>
                    <Button variant="ghost" size="sm" onClick={() => copyToClipboard(account.email)}>
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-600">كلمة المرور:</span>
                  <div className="flex items-center gap-2">
                    <code className="bg-gray-100 px-2 py-1 rounded text-xs">{account.password}</code>
                    <Button variant="ghost" size="sm" onClick={() => copyToClipboard(account.password)}>
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="text-xs text-gray-500">الصلاحيات: {account.permissions.join(", ")}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
