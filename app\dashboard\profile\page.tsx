"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { User, Briefcase, GraduationCap, Award, Plus, Edit, Trash2, Save } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { useAuth } from "@/hooks/use-auth"

interface Experience {
  id: string
  company: string
  position: string
  startDate: string
  endDate?: string
  current: boolean
  description: string
}

interface Education {
  id: string
  institution: string
  degree: string
  field: string
  startDate: string
  endDate: string
  grade?: string
}

interface Skill {
  id: string
  name: string
  level: "مبتدئ" | "متوسط" | "متقدم" | "خبير"
}

interface UserProfile {
  id: string
  name: string
  email: string
  phone?: string
  location?: string
  bio?: string
  avatar?: string
  experiences: Experience[]
  education: Education[]
  skills: Skill[]
  languages: string[]
  certifications: string[]
}

export default function ProfilePage() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfile>({
    id: user?.id || "1",
    name: user?.name || "",
    email: user?.email || "",
    phone: "",
    location: "",
    bio: "",
    experiences: [],
    education: [],
    skills: [],
    languages: [],
    certifications: [],
  })
  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState("personal")
  const [newExperience, setNewExperience] = useState<Omit<Experience, "id">>({
    company: "",
    position: "",
    startDate: "",
    endDate: "",
    current: false,
    description: "",
  })
  const [newEducation, setNewEducation] = useState<Omit<Education, "id">>({
    institution: "",
    degree: "",
    field: "",
    startDate: "",
    endDate: "",
    grade: "",
  })
  const [newSkill, setNewSkill] = useState<Omit<Skill, "id">>({
    name: "",
    level: "مبتدئ",
  })
  const [isAddingExperience, setIsAddingExperience] = useState(false)
  const [isAddingEducation, setIsAddingEducation] = useState(false)
  const [isAddingSkill, setIsAddingSkill] = useState(false)

  // Load profile from localStorage
  useEffect(() => {
    const savedProfile = localStorage.getItem(`profile-${user?.id}`)
    if (savedProfile) {
      try {
        setProfile(JSON.parse(savedProfile))
      } catch (error) {
        console.error("Error loading profile:", error)
      }
    }
  }, [user?.id])

  // Save profile to localStorage
  const saveProfile = (updatedProfile: UserProfile) => {
    setProfile(updatedProfile)
    localStorage.setItem(`profile-${user?.id}`, JSON.stringify(updatedProfile))
  }

  const handleSaveProfile = () => {
    saveProfile(profile)
    setIsEditing(false)
  }

  const addExperience = () => {
    const experience: Experience = {
      id: Date.now().toString(),
      ...newExperience,
    }
    saveProfile({
      ...profile,
      experiences: [...profile.experiences, experience],
    })
    setNewExperience({
      company: "",
      position: "",
      startDate: "",
      endDate: "",
      current: false,
      description: "",
    })
    setIsAddingExperience(false)
  }

  const removeExperience = (id: string) => {
    saveProfile({
      ...profile,
      experiences: profile.experiences.filter((exp) => exp.id !== id),
    })
  }

  const addEducation = () => {
    const education: Education = {
      id: Date.now().toString(),
      ...newEducation,
    }
    saveProfile({
      ...profile,
      education: [...profile.education, education],
    })
    setNewEducation({
      institution: "",
      degree: "",
      field: "",
      startDate: "",
      endDate: "",
      grade: "",
    })
    setIsAddingEducation(false)
  }

  const removeEducation = (id: string) => {
    saveProfile({
      ...profile,
      education: profile.education.filter((edu) => edu.id !== id),
    })
  }

  const addSkill = () => {
    const skill: Skill = {
      id: Date.now().toString(),
      ...newSkill,
    }
    saveProfile({
      ...profile,
      skills: [...profile.skills, skill],
    })
    setNewSkill({
      name: "",
      level: "مبتدئ",
    })
    setIsAddingSkill(false)
  }

  const removeSkill = (id: string) => {
    saveProfile({
      ...profile,
      skills: profile.skills.filter((skill) => skill.id !== id),
    })
  }

  const getSkillColor = (level: Skill["level"]) => {
    switch (level) {
      case "مبتدئ":
        return "bg-gray-100 text-gray-800"
      case "متوسط":
        return "bg-blue-100 text-blue-800"
      case "متقدم":
        return "bg-green-100 text-green-800"
      case "خبير":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <User className="h-6 w-6" />
              الملف الشخصي
            </h1>
            <p className="text-gray-600 mt-1">إدارة معلوماتك الشخصية والمهنية</p>
          </div>
          <Button
            onClick={isEditing ? handleSaveProfile : () => setIsEditing(true)}
            className="bg-[#C0322D] hover:bg-[#C1352F]"
          >
            {isEditing ? (
              <>
                <Save className="h-4 w-4 ml-2" />
                حفظ التغييرات
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 ml-2" />
                تعديل الملف الشخصي
              </>
            )}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="personal">المعلومات الشخصية</TabsTrigger>
            <TabsTrigger value="experience">الخبرات</TabsTrigger>
            <TabsTrigger value="education">التعليم</TabsTrigger>
            <TabsTrigger value="skills">المهارات</TabsTrigger>
          </TabsList>

          {/* Personal Information Tab */}
          <TabsContent value="personal">
            <Card>
              <CardHeader>
                <CardTitle>المعلومات الشخصية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">الاسم الكامل</Label>
                    <Input
                      id="name"
                      value={profile.name}
                      onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      value={profile.phone || ""}
                      onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                      disabled={!isEditing}
                      placeholder="أدخل رقم الهاتف"
                    />
                  </div>
                  <div>
                    <Label htmlFor="location">الموقع</Label>
                    <Input
                      id="location"
                      value={profile.location || ""}
                      onChange={(e) => setProfile({ ...profile, location: e.target.value })}
                      disabled={!isEditing}
                      placeholder="أدخل موقعك"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="bio">نبذة شخصية</Label>
                  <Textarea
                    id="bio"
                    value={profile.bio || ""}
                    onChange={(e) => setProfile({ ...profile, bio: e.target.value })}
                    disabled={!isEditing}
                    placeholder="اكتب نبذة عن نفسك..."
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Experience Tab */}
          <TabsContent value="experience">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>الخبرات المهنية</CardTitle>
                  <Dialog open={isAddingExperience} onOpenChange={setIsAddingExperience}>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-[#C0322D] hover:bg-[#C1352F]">
                        <Plus className="h-4 w-4 ml-2" />
                        إضافة خبرة
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>إضافة خبرة جديدة</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="company">الشركة</Label>
                          <Input
                            id="company"
                            value={newExperience.company}
                            onChange={(e) => setNewExperience({ ...newExperience, company: e.target.value })}
                            placeholder="اسم الشركة"
                          />
                        </div>
                        <div>
                          <Label htmlFor="position">المنصب</Label>
                          <Input
                            id="position"
                            value={newExperience.position}
                            onChange={(e) => setNewExperience({ ...newExperience, position: e.target.value })}
                            placeholder="المنصب الوظيفي"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="startDate">تاريخ البداية</Label>
                            <Input
                              id="startDate"
                              type="date"
                              value={newExperience.startDate}
                              onChange={(e) => setNewExperience({ ...newExperience, startDate: e.target.value })}
                            />
                          </div>
                          <div>
                            <Label htmlFor="endDate">تاريخ النهاية</Label>
                            <Input
                              id="endDate"
                              type="date"
                              value={newExperience.endDate}
                              onChange={(e) => setNewExperience({ ...newExperience, endDate: e.target.value })}
                              disabled={newExperience.current}
                            />
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="current"
                            checked={newExperience.current}
                            onChange={(e) => setNewExperience({ ...newExperience, current: e.target.checked })}
                          />
                          <Label htmlFor="current">أعمل حالياً في هذا المنصب</Label>
                        </div>
                        <div>
                          <Label htmlFor="description">الوصف</Label>
                          <Textarea
                            id="description"
                            value={newExperience.description}
                            onChange={(e) => setNewExperience({ ...newExperience, description: e.target.value })}
                            placeholder="وصف المهام والإنجازات..."
                            rows={3}
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={addExperience} className="flex-1">
                            إضافة
                          </Button>
                          <Button variant="outline" onClick={() => setIsAddingExperience(false)} className="flex-1">
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                {profile.experiences.length === 0 ? (
                  <div className="text-center py-8">
                    <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">لم تتم إضافة أي خبرات بعد</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {profile.experiences.map((exp) => (
                      <div key={exp.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg">{exp.position}</h3>
                            <p className="text-gray-600">{exp.company}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(exp.startDate).toLocaleDateString("ar-SA")} -{" "}
                              {exp.current ? "حتى الآن" : new Date(exp.endDate!).toLocaleDateString("ar-SA")}
                            </p>
                            {exp.description && <p className="mt-2 text-gray-700">{exp.description}</p>}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeExperience(exp.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Education Tab */}
          <TabsContent value="education">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>التعليم</CardTitle>
                  <Dialog open={isAddingEducation} onOpenChange={setIsAddingEducation}>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-[#C0322D] hover:bg-[#C1352F]">
                        <Plus className="h-4 w-4 ml-2" />
                        إضافة تعليم
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>إضافة مؤهل تعليمي</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="institution">المؤسسة التعليمية</Label>
                          <Input
                            id="institution"
                            value={newEducation.institution}
                            onChange={(e) => setNewEducation({ ...newEducation, institution: e.target.value })}
                            placeholder="اسم الجامعة أو المؤسسة"
                          />
                        </div>
                        <div>
                          <Label htmlFor="degree">الدرجة العلمية</Label>
                          <Input
                            id="degree"
                            value={newEducation.degree}
                            onChange={(e) => setNewEducation({ ...newEducation, degree: e.target.value })}
                            placeholder="بكالوريوس، ماجستير، دكتوراه..."
                          />
                        </div>
                        <div>
                          <Label htmlFor="field">التخصص</Label>
                          <Input
                            id="field"
                            value={newEducation.field}
                            onChange={(e) => setNewEducation({ ...newEducation, field: e.target.value })}
                            placeholder="مجال التخصص"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="eduStartDate">تاريخ البداية</Label>
                            <Input
                              id="eduStartDate"
                              type="date"
                              value={newEducation.startDate}
                              onChange={(e) => setNewEducation({ ...newEducation, startDate: e.target.value })}
                            />
                          </div>
                          <div>
                            <Label htmlFor="eduEndDate">تاريخ التخرج</Label>
                            <Input
                              id="eduEndDate"
                              type="date"
                              value={newEducation.endDate}
                              onChange={(e) => setNewEducation({ ...newEducation, endDate: e.target.value })}
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="grade">التقدير</Label>
                          <Input
                            id="grade"
                            value={newEducation.grade || ""}
                            onChange={(e) => setNewEducation({ ...newEducation, grade: e.target.value })}
                            placeholder="التقدير أو المعدل (اختياري)"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={addEducation} className="flex-1">
                            إضافة
                          </Button>
                          <Button variant="outline" onClick={() => setIsAddingEducation(false)} className="flex-1">
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                {profile.education.length === 0 ? (
                  <div className="text-center py-8">
                    <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">لم تتم إضافة أي مؤهلات تعليمية بعد</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {profile.education.map((edu) => (
                      <div key={edu.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg">
                              {edu.degree} في {edu.field}
                            </h3>
                            <p className="text-gray-600">{edu.institution}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(edu.startDate).toLocaleDateString("ar-SA")} -{" "}
                              {new Date(edu.endDate).toLocaleDateString("ar-SA")}
                            </p>
                            {edu.grade && <p className="text-sm text-gray-600 mt-1">التقدير: {edu.grade}</p>}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeEducation(edu.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Skills Tab */}
          <TabsContent value="skills">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>المهارات</CardTitle>
                  <Dialog open={isAddingSkill} onOpenChange={setIsAddingSkill}>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-[#C0322D] hover:bg-[#C1352F]">
                        <Plus className="h-4 w-4 ml-2" />
                        إضافة مهارة
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>إضافة مهارة جديدة</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="skillName">اسم المهارة</Label>
                          <Input
                            id="skillName"
                            value={newSkill.name}
                            onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
                            placeholder="اسم المهارة"
                          />
                        </div>
                        <div>
                          <Label htmlFor="skillLevel">مستوى المهارة</Label>
                          <Select
                            value={newSkill.level}
                            onValueChange={(value: Skill["level"]) => setNewSkill({ ...newSkill, level: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="مبتدئ">مبتدئ</SelectItem>
                              <SelectItem value="متوسط">متوسط</SelectItem>
                              <SelectItem value="متقدم">متقدم</SelectItem>
                              <SelectItem value="خبير">خبير</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={addSkill} className="flex-1">
                            إضافة
                          </Button>
                          <Button variant="outline" onClick={() => setIsAddingSkill(false)} className="flex-1">
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                {profile.skills.length === 0 ? (
                  <div className="text-center py-8">
                    <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">لم تتم إضافة أي مهارات بعد</p>
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {profile.skills.map((skill) => (
                      <div key={skill.id} className="flex items-center gap-2">
                        <Badge className={getSkillColor(skill.level)}>
                          {skill.name} - {skill.level}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSkill(skill.id)}
                          className="text-red-600 hover:text-red-700 h-6 w-6 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
