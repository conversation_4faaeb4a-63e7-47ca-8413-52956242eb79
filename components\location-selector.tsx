"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { MapPin, Plus, Settings } from "lucide-react"

interface LocationGroup {
  id: string
  name: string
  cities: string[]
}

interface LocationSelectorProps {
  selectedLocation: string
  onLocationChange: (location: string) => void
  placeholder?: string
}

export function LocationSelector({
  selectedLocation,
  onLocationChange,
  placeholder = "اختر الموقع",
}: LocationSelectorProps) {
  const [locationGroups] = useState<LocationGroup[]>([
    {
      id: "saudi_major",
      name: "المدن الرئيسية - السعودية",
      cities: ["الرياض", "جدة", "الدمام", "مكة المكرمة", "المدينة المنورة", "الطائف", "بريدة", "تبوك"],
    },
    {
      id: "saudi_eastern",
      name: "المنطقة الشرقية - السعودية",
      cities: ["الدمام", "الخبر", "الظهران", "الجبيل", "الأحساء", "حفر الباطن", "القطيف", "رأس تنورة"],
    },
    {
      id: "saudi_western",
      name: "المنطقة الغربية - السعودية",
      cities: ["جدة", "مكة المكرمة", "المدينة المنورة", "الطائف", "ينبع", "رابغ", "الليث", "القنفذة"],
    },
    {
      id: "saudi_central",
      name: "المنطقة الوسطى - السعودية",
      cities: ["الرياض", "بريدة", "عنيزة", "الرس", "المجمعة", "الزلفي", "شقراء", "الدوادمي"],
    },
    {
      id: "saudi_northern",
      name: "المنطقة الشمالية - السعودية",
      cities: ["تبوك", "عرعر", "سكاكا", "حائل", "رفحاء", "طريف", "القريات", "الجوف"],
    },
    {
      id: "saudi_southern",
      name: "المنطقة الجنوبية - السعودية",
      cities: ["أبها", "خميس مشيط", "نجران", "جازان", "الباحة", "بيشة", "صبيا", "محايل عسير"],
    },
    {
      id: "gcc",
      name: "دول الخليج العربي",
      cities: ["دبي", "أبوظبي", "الشارقة", "الدوحة", "الكويت", "المنامة", "مسقط", "صلالة"],
    },
    {
      id: "remote",
      name: "العمل عن بُعد",
      cities: ["عمل عن بُعد - السعودية", "عمل عن بُعد - الخليج", "عمل عن بُعد - عالمي", "هجين - مكتب وعن بُعد"],
    },
  ])

  const [selectedGroup, setSelectedGroup] = useState<string>("")
  const [customLocation, setCustomLocation] = useState("")
  const [isManageDialogOpen, setIsManageDialogOpen] = useState(false)

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroup(groupId)
  }

  const handleLocationSelect = (location: string) => {
    onLocationChange(location)
  }

  const handleAddCustomLocation = () => {
    if (customLocation.trim()) {
      onLocationChange(customLocation.trim())
      setCustomLocation("")
    }
  }

  const getAllCities = () => {
    return locationGroups.flatMap((group) => group.cities)
  }

  const getSelectedGroup = () => {
    return locationGroups.find((group) => group.cities.includes(selectedLocation))
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>الموقع</Label>
        <Button variant="outline" size="sm" onClick={() => setIsManageDialogOpen(true)}>
          <Settings className="h-4 w-4 mr-2" />
          إدارة المواقع
        </Button>
      </div>

      {/* Group Selector */}
      <div className="space-y-2">
        <Label className="text-sm">اختر منطقة</Label>
        <Select value={selectedGroup} onValueChange={handleGroupSelect}>
          <SelectTrigger>
            <SelectValue placeholder="اختر منطقة أولاً" />
          </SelectTrigger>
          <SelectContent>
            {locationGroups.map((group) => (
              <SelectItem key={group.id} value={group.id}>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  {group.name} ({group.cities.length} مدينة)
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* City Selector */}
      {selectedGroup && (
        <div className="space-y-2">
          <Label className="text-sm">اختر المدينة</Label>
          <Select value={selectedLocation} onValueChange={handleLocationSelect}>
            <SelectTrigger>
              <SelectValue placeholder="اختر المدينة" />
            </SelectTrigger>
            <SelectContent>
              {locationGroups
                .find((g) => g.id === selectedGroup)
                ?.cities.map((city) => (
                  <SelectItem key={city} value={city}>
                    {city}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Quick Select All Cities */}
      <div className="space-y-2">
        <Label className="text-sm">أو اختر من جميع المدن</Label>
        <Select value={selectedLocation} onValueChange={handleLocationSelect}>
          <SelectTrigger>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent className="max-h-64">
            {getAllCities().map((city) => (
              <SelectItem key={city} value={city}>
                <div className="flex items-center gap-2">
                  <MapPin className="h-3 w-3" />
                  {city}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Custom Location Input */}
      <div className="flex gap-2">
        <Input
          placeholder="أضف موقع مخصص"
          value={customLocation}
          onChange={(e) => setCustomLocation(e.target.value)}
          onKeyPress={(e) => e.key === "Enter" && handleAddCustomLocation()}
        />
        <Button onClick={handleAddCustomLocation} disabled={!customLocation.trim()}>
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Selected Location Display */}
      {selectedLocation && (
        <div className="p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-green-600" />
            <span className="font-medium text-green-800 dark:text-green-200">{selectedLocation}</span>
            {getSelectedGroup() && (
              <span className="text-sm text-green-600 dark:text-green-300">({getSelectedGroup()?.name})</span>
            )}
          </div>
        </div>
      )}

      {/* Manage Locations Dialog */}
      <Dialog open={isManageDialogOpen} onOpenChange={setIsManageDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>إدارة المواقع والمناطق</DialogTitle>
            <DialogDescription>يمكنك إضافة أو تعديل المناطق والمدن المختلفة</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {locationGroups.map((group) => (
              <div key={group.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">{group.name}</h4>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">{group.cities.length} مدينة</span>
                    <Button variant="outline" size="sm">
                      تعديل
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                  {group.cities.map((city) => (
                    <div key={city} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm">
                      <MapPin className="h-3 w-3" />
                      {city}
                    </div>
                  ))}
                </div>
              </div>
            ))}
            <Button className="w-full bg-[#C0322D] hover:bg-[#C1352F]">
              <Plus className="h-4 w-4 mr-2" />
              إضافة منطقة جديدة
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
