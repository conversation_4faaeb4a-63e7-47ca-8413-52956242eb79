"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { BarChart3, TrendingUp, Users, Briefcase, Download, DollarSign, Target, Loader2 } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { PDFExportService } from "@/lib/pdf-export"
import { useAuth } from "@/hooks/use-auth"

export default function ReportsPage() {
  const { hasPermission } = useAuth()
  const [isExporting, setIsExporting] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState("monthly")

  const monthlyStats = [
    { month: "يناير", jobs: 15, candidates: 45, hired: 8, revenue: 120000 },
    { month: "فبراير", jobs: 18, candidates: 52, hired: 12, revenue: 180000 },
    { month: "مارس", jobs: 22, candidates: 68, hired: 15, revenue: 225000 },
    { month: "أبريل", jobs: 20, candidates: 58, hired: 11, revenue: 165000 },
  ]

  const topClients = [
    { name: "شركة التقنية المتقدمة", jobs: 8, revenue: 120000, satisfaction: 95 },
    { name: "مجموعة الأعمال الذكية", jobs: 6, revenue: 90000, satisfaction: 92 },
    { name: "شركة الإنشاءات الحديثة", jobs: 4, revenue: 60000, satisfaction: 88 },
  ]

  const performanceMetrics = [
    { metric: "معدل نجاح التوظيف", value: 85, target: 90, unit: "%" },
    { metric: "متوسط وقت التوظيف", value: 21, target: 30, unit: "يوم" },
    { metric: "رضا العملاء", value: 92, target: 95, unit: "%" },
    { metric: "معدل الاحتفاظ بالموظفين", value: 88, target: 85, unit: "%" },
  ]

  const handleExportPDF = async () => {
    setIsExporting(true)
    try {
      const reportData = {
        title: "تقرير الأداء الشهري",
        subtitle: `فترة التقرير: ${selectedPeriod === "monthly" ? "شهري" : selectedPeriod === "quarterly" ? "ربع سنوي" : "سنوي"}`,
        data: monthlyStats,
        summary: {
          "إجمالي الوظائف": monthlyStats.reduce((sum, stat) => sum + stat.jobs, 0),
          "إجمالي المرشحين": monthlyStats.reduce((sum, stat) => sum + stat.candidates, 0),
          "إجمالي الموظفين": monthlyStats.reduce((sum, stat) => sum + stat.hired, 0),
          "إجمالي الإيرادات": `${monthlyStats.reduce((sum, stat) => sum + stat.revenue, 0).toLocaleString()} ريال`,
        },
      }

      await PDFExportService.exportToPDF(reportData)
    } catch (error) {
      alert(error instanceof Error ? error.message : "حدث خطأ في التصدير")
    } finally {
      setIsExporting(false)
    }
  }

  const handleExportExcel = async () => {
    setIsExporting(true)
    try {
      const reportData = {
        title: "تقرير الأداء الشهري",
        data: monthlyStats.map((stat) => ({
          الشهر: stat.month,
          الوظائف: stat.jobs,
          المرشحين: stat.candidates,
          الموظفين: stat.hired,
          الإيرادات: stat.revenue,
        })),
      }

      await PDFExportService.exportToExcel(reportData)
    } catch (error) {
      alert(error instanceof Error ? error.message : "حدث خطأ في التصدير")
    } finally {
      setIsExporting(false)
    }
  }

  if (!hasPermission("reports")) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">غير مصرح</h2>
            <p className="text-gray-600 dark:text-gray-300">ليس لديك صلاحية للوصول إلى التقارير</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 p-4 md:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">التقارير والتحليلات</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">تحليل شامل لأداء الشركة والعمليات</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">أسبوعي</SelectItem>
                <SelectItem value="monthly">شهري</SelectItem>
                <SelectItem value="quarterly">ربع سنوي</SelectItem>
                <SelectItem value="yearly">سنوي</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={handleExportPDF}
              disabled={isExporting}
              className="w-full sm:w-auto bg-transparent"
            >
              {isExporting ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Download className="h-4 w-4 mr-2" />}
              تصدير PDF
            </Button>
            <Button
              className="bg-[#C0322D] hover:bg-[#C1352F] w-full sm:w-auto"
              onClick={handleExportExcel}
              disabled={isExporting}
            >
              {isExporting ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Download className="h-4 w-4 mr-2" />}
              تصدير Excel
            </Button>
          </div>
        </div>

        {/* Key Performance Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {performanceMetrics.map((metric, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 bg-[#C0322D]/10 rounded-lg">
                    <Target className="h-5 w-5 text-[#C0322D]" />
                  </div>
                  <Badge variant={metric.value >= metric.target ? "default" : "secondary"}>
                    {metric.value >= metric.target ? "مُحقق" : "تحت الهدف"}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900 dark:text-white">{metric.metric}</h3>
                  <div className="flex items-baseline space-x-2 rtl:space-x-reverse">
                    <span className="text-2xl font-bold text-gray-900 dark:text-white">{metric.value}</span>
                    <span className="text-sm text-gray-500">{metric.unit}</span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>
                        الهدف: {metric.target}
                        {metric.unit}
                      </span>
                      <span>{Math.round((metric.value / metric.target) * 100)}%</span>
                    </div>
                    <Progress value={(metric.value / metric.target) * 100} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Monthly Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                الأداء الشهري
              </CardTitle>
              <CardDescription>إحصائيات الأشهر الأربعة الماضية</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyStats.map((stat, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">{stat.month}</h4>
                      <Badge variant="outline">{stat.revenue.toLocaleString()} ريال</Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <Briefcase className="h-4 w-4 text-blue-600 mr-1" />
                        </div>
                        <div className="font-medium">{stat.jobs}</div>
                        <div className="text-gray-500">وظيفة</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <Users className="h-4 w-4 text-green-600 mr-1" />
                        </div>
                        <div className="font-medium">{stat.candidates}</div>
                        <div className="text-gray-500">مرشح</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <TrendingUp className="h-4 w-4 text-purple-600 mr-1" />
                        </div>
                        <div className="font-medium">{stat.hired}</div>
                        <div className="text-gray-500">موظف</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Clients */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                أفضل العملاء
              </CardTitle>
              <CardDescription>العملاء الأكثر نشاطاً وربحية</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topClients.map((client, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium">{client.name}</h4>
                        <p className="text-sm text-gray-500">{client.jobs} وظائف نشطة</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">{client.revenue.toLocaleString()} ريال</Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>مستوى الرضا</span>
                        <span>{client.satisfaction}%</span>
                      </div>
                      <Progress value={client.satisfaction} className="h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Revenue Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              تحليل الإيرادات
            </CardTitle>
            <CardDescription>تفصيل الإيرادات حسب المصدر والفترة</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium">الإيرادات حسب الخدمة</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">التوظيف المباشر</span>
                    <span className="font-medium">450,000 ريال</span>
                  </div>
                  <Progress value={75} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-sm">الاستشارات</span>
                    <span className="font-medium">120,000 ريال</span>
                  </div>
                  <Progress value={20} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-sm">التدريب</span>
                    <span className="font-medium">30,000 ريال</span>
                  </div>
                  <Progress value={5} className="h-2" />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">الإيرادات حسب القطاع</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">تقنية المعلومات</span>
                    <span className="font-medium">300,000 ريال</span>
                  </div>
                  <Progress value={50} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-sm">الخدمات المالية</span>
                    <span className="font-medium">180,000 ريال</span>
                  </div>
                  <Progress value={30} className="h-2" />

                  <div className="flex justify-between items-center">
                    <span className="text-sm">الإنشاءات</span>
                    <span className="font-medium">120,000 ريال</span>
                  </div>
                  <Progress value={20} className="h-2" />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">النمو الشهري</h4>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">+15%</div>
                  <p className="text-sm text-gray-500">مقارنة بالشهر الماضي</p>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>الهدف السنوي</span>
                    <span>2,400,000 ريال</span>
                  </div>
                  <Progress value={25} className="h-2" />
                  <p className="text-xs text-gray-500">تم تحقيق 25% من الهدف السنوي</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
