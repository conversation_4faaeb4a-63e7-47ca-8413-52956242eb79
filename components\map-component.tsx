"use client"

import { useEffect, useRef, useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { MapPin, Search } from "lucide-react"

interface Location {
  id: string
  name: string
  lat: number
  lng: number
  type: "office" | "client" | "candidate"
  description?: string
}

const defaultLocations: Location[] = [
  {
    id: "1",
    name: "مكتب ياس العالمي الرئيسي",
    lat: 24.7136,
    lng: 46.6753,
    type: "office",
    description: "المكتب الرئيسي في الرياض",
  },
  {
    id: "2",
    name: "فرع جدة",
    lat: 21.3891,
    lng: 39.8579,
    type: "office",
    description: "فرع جدة",
  },
  {
    id: "3",
    name: "شركة التقنية المتقدمة",
    lat: 24.7744,
    lng: 46.7386,
    type: "client",
    description: "عميل في الرياض",
  },
]

export function MapComponent() {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const [locations, setLocations] = useState<Location[]>(defaultLocations)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    let isMounted = true

    const initializeMap = async () => {
      try {
        // Dynamically import Leaflet
        const L = await import("leaflet")

        // Import CSS
        await import("leaflet/dist/leaflet.css")

        if (!isMounted || !mapRef.current) return

        // Initialize map
        const map = L.map(mapRef.current).setView([24.7136, 46.6753], 6)

        // Add OpenStreetMap tiles
        L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
          attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19,
        }).addTo(map)

        // Custom icons
        const officeIcon = L.divIcon({
          html: `<div class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-xs font-bold shadow-lg">🏢</div>`,
          className: "custom-marker",
          iconSize: [32, 32],
          iconAnchor: [16, 16],
        })

        const clientIcon = L.divIcon({
          html: `<div class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-xs font-bold shadow-lg">🏪</div>`,
          className: "custom-marker",
          iconSize: [32, 32],
          iconAnchor: [16, 16],
        })

        const candidateIcon = L.divIcon({
          html: `<div class="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-xs font-bold shadow-lg">👤</div>`,
          className: "custom-marker",
          iconSize: [32, 32],
          iconAnchor: [16, 16],
        })

        // Add markers
        locations.forEach((location) => {
          let icon = officeIcon
          if (location.type === "client") icon = clientIcon
          if (location.type === "candidate") icon = candidateIcon

          const marker = L.marker([location.lat, location.lng], { icon }).addTo(map)

          marker.bindPopup(`
            <div class="p-2">
              <h3 class="font-bold text-sm mb-1">${location.name}</h3>
              <p class="text-xs text-gray-600 mb-2">${location.description || ""}</p>
              <span class="inline-block px-2 py-1 text-xs rounded-full ${
                location.type === "office"
                  ? "bg-blue-100 text-blue-800"
                  : location.type === "client"
                    ? "bg-green-100 text-green-800"
                    : "bg-purple-100 text-purple-800"
              }">
                ${location.type === "office" ? "مكتب" : location.type === "client" ? "عميل" : "مرشح"}
              </span>
            </div>
          `)

          marker.on("click", () => {
            setSelectedLocation(location)
          })
        })

        mapInstanceRef.current = map
        setIsLoading(false)
      } catch (error) {
        console.error("Error initializing map:", error)
        setIsLoading(false)
      }
    }

    initializeMap()

    return () => {
      isMounted = false
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [locations])

  const filteredLocations = locations.filter((location) =>
    location.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleLocationClick = (location: Location) => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.setView([location.lat, location.lng], 12)
      setSelectedLocation(location)
    }
  }

  const getLocationTypeColor = (type: string) => {
    switch (type) {
      case "office":
        return "bg-blue-100 text-blue-800"
      case "client":
        return "bg-green-100 text-green-800"
      case "candidate":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getLocationTypeName = (type: string) => {
    switch (type) {
      case "office":
        return "مكتب"
      case "client":
        return "عميل"
      case "candidate":
        return "مرشح"
      default:
        return type
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Map */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              الخريطة التفاعلية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              {isLoading && (
                <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center z-10">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-gray-600">جاري تحميل الخريطة...</p>
                  </div>
                </div>
              )}
              <div ref={mapRef} className="h-96 w-full rounded-lg bg-gray-100" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Locations List */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle>المواقع</CardTitle>
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في المواقع..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {filteredLocations.map((location) => (
                <div
                  key={location.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors hover:bg-gray-50 ${
                    selectedLocation?.id === location.id ? "border-primary bg-primary/5" : "border-gray-200"
                  }`}
                  onClick={() => handleLocationClick(location)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-sm">{location.name}</h4>
                    <Badge className={getLocationTypeColor(location.type)} variant="secondary">
                      {getLocationTypeName(location.type)}
                    </Badge>
                  </div>
                  {location.description && <p className="text-xs text-gray-600 mb-2">{location.description}</p>}
                  <div className="text-xs text-gray-500">
                    {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
                  </div>
                </div>
              ))}
            </div>

            {filteredLocations.length === 0 && (
              <div className="text-center py-8">
                <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">لا توجد مواقع مطابقة للبحث</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
