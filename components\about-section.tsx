"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Award, Globe, Clock, Star } from "lucide-react"
import { motion } from "framer-motion"

export function AboutSection() {
  const achievements = [
    {
      icon: Award,
      number: "10+",
      label: "سنوات من الخبرة",
      labelEn: "Years of Experience",
    },
    {
      icon: Globe,
      number: "25+",
      label: "دولة نعمل بها",
      labelEn: "Countries Served",
    },
    {
      icon: Clock,
      number: "24/7",
      label: "دعم مستمر",
      labelEn: "Continuous Support",
    },
    {
      icon: Star,
      number: "4.9",
      label: "تقييم العملاء",
      labelEn: "Client Rating",
    },
  ]

  return (
    <section className="py-20 bg-white dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">من نحن؟</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              شركة Yas Global Partner هي شركة رائدة في مجال حلول المواهب والاستشارات في الموارد البشرية. نحن نساعد
              الشركات على بناء فرق عمل متميزة من خلال خدماتنا المتخصصة في التوظيف وإدارة المواهب.
            </p>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
              مع خبرة تزيد عن عقد من الزمن، نفخر بتقديم حلول مبتكرة ومخصصة تلبي احتياجات عملائنا المتنوعة في مختلف
              القطاعات والصناعات.
            </p>

            <div className="grid grid-cols-2 gap-6">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="text-center p-4 hover:shadow-md transition-shadow">
                    <CardContent className="p-0">
                      <achievement.icon className="h-8 w-8 text-[#C0322D] mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">{achievement.number}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">{achievement.label}</div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            <div className="bg-gradient-to-br from-[#C0322D] to-[#C1352F] rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">رؤيتنا</h3>
              <p className="text-lg mb-6 opacity-90">
                أن نكون الشريك الأول والأكثر ثقة في مجال حلول الموارد البشرية والتوظيف في المنطقة
              </p>

              <h3 className="text-2xl font-bold mb-4">مهمتنا</h3>
              <p className="text-lg opacity-90">
                تمكين الشركات من تحقيق أهدافها من خلال توفير أفضل المواهب وتقديم حلول مبتكرة في مجال الموارد البشرية
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
