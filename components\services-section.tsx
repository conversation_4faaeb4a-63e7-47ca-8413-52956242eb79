"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Search, Target, BarChart3, Headphones, Shield } from "lucide-react"
import { motion } from "framer-motion"

export function ServicesSection() {
  const services = [
    {
      icon: Search,
      title: "البحث والتوظيف",
      description: "خدمات شاملة للبحث عن المواهب المتميزة وتوظيفها في مختلف التخصصات",
      titleEn: "Talent Acquisition",
    },
    {
      icon: Users,
      title: "إدارة الموارد البشرية",
      description: "حلول متكاملة لإدارة الموارد البشرية وتطوير السياسات والإجراءات",
      titleEn: "HR Management",
    },
    {
      icon: Target,
      title: "استشارات الأعمال",
      description: "استشارات متخصصة في تطوير الأعمال وتحسين الأداء المؤسسي",
      titleEn: "Business Consulting",
    },
    {
      icon: BarChart3,
      title: "تحليل الأداء",
      description: "تقييم وتحليل أداء الموظفين وتقديم التوصيات للتحسين",
      titleEn: "Performance Analytics",
    },
    {
      icon: Headphones,
      title: "الدعم والتدريب",
      description: "برامج تدريبية متخصصة ودعم مستمر لتطوير المهارات",
      titleEn: "Training & Support",
    },
    {
      icon: Shield,
      title: "الامتثال والحوكمة",
      description: "ضمان الامتثال للقوانين واللوائح وتطبيق معايير الحوكمة",
      titleEn: "Compliance & Governance",
    },
  ]

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">خدماتنا المتميزة</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            نقدم مجموعة شاملة من الخدمات المتخصصة في مجال الموارد البشرية والتوظيف
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300 border-l-4 border-l-[#C0322D]">
                <CardHeader>
                  <div className="w-12 h-12 bg-[#C0322D]/10 rounded-lg flex items-center justify-center mb-4">
                    <service.icon className="h-6 w-6 text-[#C0322D]" />
                  </div>
                  <CardTitle className="text-xl text-gray-900 dark:text-white">{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {service.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
