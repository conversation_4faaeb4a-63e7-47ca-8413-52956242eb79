"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  FileText,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Building
} from "lucide-react"

export default function EmployeeContractsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isNewContractDialogOpen, setIsNewContractDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedContract, setSelectedContract] = useState<any>(null)

  const handleViewContract = (contract: any) => {
    setSelectedContract(contract)
    setIsViewDialogOpen(true)
  }

  const handleEditContract = (contract: any) => {
    setSelectedContract(contract)
    setIsEditDialogOpen(true)
  }

  const handleDownloadContract = (contract: any) => {
    // محاكاة تحميل العقد
    const element = document.createElement('a')
    const file = new Blob([`عقد الموظف: ${contract.employeeName}\nالشركة: ${contract.company}\nالمنصب: ${contract.position}`], {type: 'text/plain'})
    element.href = URL.createObjectURL(file)
    element.download = `contract-${contract.employeeId}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  // بيانات وهمية لعقود الموظفين
  const employeeContracts = [
    {
      id: 1,
      employeeName: "أحمد محمد السالم",
      employeeId: "EMP001",
      company: "أرامكو السعودية",
      position: "مهندس بترول",
      contractType: "دائم",
      status: "active",
      startDate: "2024-01-15",
      endDate: "2025-01-14",
      salary: 15000,
      currency: "SAR",
      benefits: ["تأمين صحي", "بدل سكن", "بدل مواصلات"],
      signedDate: "2024-01-10",
      approvalStatus: "approved"
    },
    {
      id: 2,
      employeeName: "فاطمة عبدالله النمر",
      employeeId: "EMP002",
      company: "سابك",
      position: "محاسبة",
      contractType: "مؤقت",
      status: "pending",
      startDate: "2024-03-01",
      endDate: "2024-08-31",
      salary: 12000,
      currency: "SAR",
      benefits: ["تأمين صحي", "بدل مواصلات"],
      signedDate: null,
      approvalStatus: "pending"
    },
    {
      id: 3,
      employeeName: "خالد سعد الغامدي",
      employeeId: "EMP003",
      company: "البنك الأهلي",
      position: "مطور برمجيات",
      contractType: "دائم",
      status: "completed",
      startDate: "2023-06-01",
      endDate: "2024-05-31",
      salary: 18000,
      currency: "SAR",
      benefits: ["تأمين صحي", "بدل سكن", "بدل مواصلات", "بونص سنوي"],
      signedDate: "2023-05-25",
      approvalStatus: "approved"
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">في الانتظار</Badge>
      case "completed":
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case "terminated":
        return <Badge className="bg-red-100 text-red-800">منتهي</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getApprovalBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800">موافق عليه</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">في الانتظار</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800">مرفوض</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const filteredContracts = employeeContracts.filter(contract =>
    contract.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.position.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">عقود الموظفين</h1>
            <p className="text-gray-600">إدارة عقود الموظفين مع الشركات</p>
          </div>
          <Dialog open={isNewContractDialogOpen} onOpenChange={setIsNewContractDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-red-600 hover:bg-red-700">
                <Plus className="w-4 h-4 mr-2" />
                عقد موظف جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>إنشاء عقد موظف جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-6">
                <Tabs defaultValue="employee" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="employee">بيانات الموظف</TabsTrigger>
                    <TabsTrigger value="contract">تفاصيل العقد</TabsTrigger>
                    <TabsTrigger value="benefits">المزايا والراتب</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="employee" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="employee-name">اسم الموظف</Label>
                        <Input id="employee-name" placeholder="أدخل اسم الموظف" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="employee-id">رقم الهوية</Label>
                        <Input id="employee-id" placeholder="رقم الهوية الوطنية" />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="phone">رقم الهاتف</Label>
                        <Input id="phone" placeholder="+966 5X XXX XXXX" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">البريد الإلكتروني</Label>
                        <Input id="email" type="email" placeholder="<EMAIL>" />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="address">العنوان</Label>
                      <Textarea id="address" placeholder="العنوان الكامل" rows={2} />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="contract" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="company">الشركة</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر الشركة" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="aramco">أرامكو السعودية</SelectItem>
                            <SelectItem value="sabic">سابك</SelectItem>
                            <SelectItem value="alahli">البنك الأهلي</SelectItem>
                            <SelectItem value="stc">الاتصالات السعودية</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="position">المنصب</Label>
                        <Input id="position" placeholder="المنصب الوظيفي" />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="contract-type">نوع العقد</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر نوع العقد" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="permanent">دائم</SelectItem>
                            <SelectItem value="temporary">مؤقت</SelectItem>
                            <SelectItem value="contract">تعاقد</SelectItem>
                            <SelectItem value="part-time">دوام جزئي</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="department">القسم</Label>
                        <Input id="department" placeholder="القسم أو الإدارة" />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="start-date">تاريخ البداية</Label>
                        <Input id="start-date" type="date" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="end-date">تاريخ النهاية</Label>
                        <Input id="end-date" type="date" />
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="benefits" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="salary">الراتب الأساسي</Label>
                        <div className="flex gap-2">
                          <Input id="salary" type="number" placeholder="0" className="flex-1" />
                          <Select defaultValue="SAR">
                            <SelectTrigger className="w-24">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="SAR">ريال سعودي</SelectItem>
                              <SelectItem value="USD">دولار أمريكي</SelectItem>
                              <SelectItem value="EUR">يورو</SelectItem>
                              <SelectItem value="AED">درهم إماراتي</SelectItem>
                              <SelectItem value="KWD">دينار كويتي</SelectItem>
                              <SelectItem value="QAR">ريال قطري</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="housing-allowance">بدل السكن</Label>
                        <Input id="housing-allowance" type="number" placeholder="0" />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="transport-allowance">بدل المواصلات</Label>
                        <Input id="transport-allowance" type="number" placeholder="0" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="annual-bonus">البونص السنوي</Label>
                        <Input id="annual-bonus" type="number" placeholder="0" />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>المزايا الإضافية</Label>
                      <div className="grid grid-cols-2 gap-2">
                        <label className="flex items-center space-x-2 space-x-reverse">
                          <input type="checkbox" className="rounded" />
                          <span>تأمين صحي</span>
                        </label>
                        <label className="flex items-center space-x-2 space-x-reverse">
                          <input type="checkbox" className="rounded" />
                          <span>تأمين على الحياة</span>
                        </label>
                        <label className="flex items-center space-x-2 space-x-reverse">
                          <input type="checkbox" className="rounded" />
                          <span>إجازة سنوية مدفوعة</span>
                        </label>
                        <label className="flex items-center space-x-2 space-x-reverse">
                          <input type="checkbox" className="rounded" />
                          <span>تدريب وتطوير</span>
                        </label>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex gap-2 pt-4">
                  <Button 
                    className="bg-red-600 hover:bg-red-700 flex-1"
                    onClick={() => setIsNewContractDialogOpen(false)}
                  >
                    إنشاء العقد
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsNewContractDialogOpen(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي العقود</p>
                  <p className="text-2xl font-bold">156</p>
                </div>
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">العقود النشطة</p>
                  <p className="text-2xl font-bold text-green-600">89</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                  <p className="text-2xl font-bold text-orange-600">23</p>
                </div>
                <Clock className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">متوسط الراتب</p>
                  <p className="text-2xl font-bold">14.5K ر.س</p>
                </div>
                <DollarSign className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="البحث في عقود الموظفين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            تصفية
          </Button>
        </div>

        {/* Employee Contracts List */}
        <div className="grid gap-6">
          {filteredContracts.map((contract) => (
            <Card key={contract.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <User className="w-6 h-6 text-red-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{contract.employeeName}</h3>
                      <p className="text-gray-600">{contract.position} - {contract.company}</p>
                      <p className="text-sm text-gray-500">رقم الموظف: {contract.employeeId}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(contract.status)}
                    {getApprovalBadge(contract.approvalStatus)}
                    <Button variant="outline" size="sm" onClick={() => handleViewContract(contract)}>
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleEditContract(contract)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDownloadContract(contract)}>
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">نوع العقد</p>
                    <p className="font-medium">{contract.contractType}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">تاريخ البداية</p>
                    <p className="font-medium">{contract.startDate}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">تاريخ النهاية</p>
                    <p className="font-medium">{contract.endDate}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">الراتب</p>
                    <p className="font-medium">{contract.salary.toLocaleString()} {contract.currency}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">تاريخ التوقيع</p>
                    <p className="font-medium">{contract.signedDate || "لم يوقع بعد"}</p>
                  </div>
                </div>
                
                <div className="mt-4">
                  <p className="text-gray-500 text-sm mb-2">المزايا:</p>
                  <div className="flex flex-wrap gap-2">
                    {contract.benefits.map((benefit, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {benefit}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View Contract Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>عرض تفاصيل العقد</DialogTitle>
            </DialogHeader>
            {selectedContract && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>اسم الموظف</Label>
                    <p className="font-medium">{selectedContract.employeeName}</p>
                  </div>
                  <div>
                    <Label>رقم الموظف</Label>
                    <p className="font-medium">{selectedContract.employeeId}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>الشركة</Label>
                    <p className="font-medium">{selectedContract.company}</p>
                  </div>
                  <div>
                    <Label>المنصب</Label>
                    <p className="font-medium">{selectedContract.position}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>الراتب</Label>
                    <p className="font-medium">{selectedContract.salary.toLocaleString()} {selectedContract.currency}</p>
                  </div>
                  <div>
                    <Label>نوع العقد</Label>
                    <p className="font-medium">{selectedContract.contractType}</p>
                  </div>
                </div>
                <div>
                  <Label>المزايا</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {selectedContract.benefits.map((benefit: string, index: number) => (
                      <Badge key={index} variant="secondary">{benefit}</Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Edit Contract Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>تحرير العقد</DialogTitle>
            </DialogHeader>
            {selectedContract && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>اسم الموظف</Label>
                    <Input defaultValue={selectedContract.employeeName} />
                  </div>
                  <div className="space-y-2">
                    <Label>المنصب</Label>
                    <Input defaultValue={selectedContract.position} />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>الراتب</Label>
                    <Input type="number" defaultValue={selectedContract.salary} />
                  </div>
                  <div className="space-y-2">
                    <Label>العملة</Label>
                    <Select defaultValue={selectedContract.currency}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SAR">ريال سعودي</SelectItem>
                        <SelectItem value="USD">دولار أمريكي</SelectItem>
                        <SelectItem value="EUR">يورو</SelectItem>
                        <SelectItem value="AED">درهم إماراتي</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex gap-2 pt-4">
                  <Button className="bg-red-600 hover:bg-red-700 flex-1" onClick={() => setIsEditDialogOpen(false)}>
                    حفظ التغييرات
                  </Button>
                  <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
