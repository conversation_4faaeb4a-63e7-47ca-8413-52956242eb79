"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Plus, 
  Edit, 
  Trash2, 
  Heart, 
  DollarSign, 
  Clock, 
  BookOpen, 
  Coffee, 
  Shield,
  Smile,
  Eye,
  TrendingUp,
  PiggyBank,
  BarChart3,
  Home,
  Calendar,
  Thermometer,
  Users,
  Award,
  Dumbbell,
  Car,
  Phone,
  Percent
} from "lucide-react"
import { JobBenefit, defaultJobBenefits, benefitCategories } from "@/data/job-benefits"

interface BenefitsManagerProps {
  selectedBenefits?: string[]
  onBenefitsChange?: (benefits: string[]) => void
  mode?: 'select' | 'manage'
}

const iconMap = {
  Heart, DollarSign, Clock, BookOpen, Coffee, Shield, Smile, Eye, TrendingUp,
  PiggyBank, BarChart3, Home, Calendar, Thermometer, Users, Award, Dumbbell,
  Car, Phone, Percent, Plus
}

export function BenefitsManager({ 
  selectedBenefits = [], 
  onBenefitsChange, 
  mode = 'select' 
}: BenefitsManagerProps) {
  const [benefits, setBenefits] = useState<JobBenefit[]>(defaultJobBenefits)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingBenefit, setEditingBenefit] = useState<JobBenefit | null>(null)
  const [newBenefit, setNewBenefit] = useState<Partial<JobBenefit>>({
    name: '',
    category: 'other',
    description: '',
    icon: 'Plus',
    isActive: true,
  })

  const handleBenefitToggle = (benefitId: string) => {
    if (!onBenefitsChange) return
    
    const updatedBenefits = selectedBenefits.includes(benefitId)
      ? selectedBenefits.filter(id => id !== benefitId)
      : [...selectedBenefits, benefitId]
    
    onBenefitsChange(updatedBenefits)
  }

  const handleAddBenefit = () => {
    if (!newBenefit.name || !newBenefit.description) return

    const benefit: JobBenefit = {
      id: `custom-${Date.now()}`,
      name: newBenefit.name,
      category: newBenefit.category as JobBenefit['category'],
      description: newBenefit.description,
      icon: newBenefit.icon || 'Plus',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    setBenefits([...benefits, benefit])
    setNewBenefit({
      name: '',
      category: 'other',
      description: '',
      icon: 'Plus',
      isActive: true,
    })
    setIsDialogOpen(false)
  }

  const handleEditBenefit = (benefit: JobBenefit) => {
    setEditingBenefit(benefit)
    setNewBenefit(benefit)
    setIsDialogOpen(true)
  }

  const handleUpdateBenefit = () => {
    if (!editingBenefit || !newBenefit.name || !newBenefit.description) return

    const updatedBenefits = benefits.map(b => 
      b.id === editingBenefit.id 
        ? { ...b, ...newBenefit, updatedAt: new Date() }
        : b
    )

    setBenefits(updatedBenefits)
    setEditingBenefit(null)
    setNewBenefit({
      name: '',
      category: 'other',
      description: '',
      icon: 'Plus',
      isActive: true,
    })
    setIsDialogOpen(false)
  }

  const handleDeleteBenefit = (benefitId: string) => {
    setBenefits(benefits.filter(b => b.id !== benefitId))
    if (onBenefitsChange) {
      onBenefitsChange(selectedBenefits.filter(id => id !== benefitId))
    }
  }

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || iconMap.Plus
    return IconComponent
  }

  const getCategoryIcon = (category: string) => {
    const categoryData = benefitCategories.find(c => c.id === category)
    return getIcon(categoryData?.icon || 'Plus')
  }

  const groupedBenefits = benefits.reduce((acc, benefit) => {
    if (!acc[benefit.category]) {
      acc[benefit.category] = []
    }
    acc[benefit.category].push(benefit)
    return acc
  }, {} as Record<string, JobBenefit[]>)

  if (mode === 'select') {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">المميزات الوظيفية</Label>
          {mode === 'manage' && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" onClick={() => setEditingBenefit(null)}>
                  <Plus className="w-4 h-4 ml-2" />
                  إضافة ميزة
                </Button>
              </DialogTrigger>
            </Dialog>
          )}
        </div>

        <div className="space-y-4">
          {benefitCategories.map(category => {
            const categoryBenefits = groupedBenefits[category.id] || []
            if (categoryBenefits.length === 0) return null

            const CategoryIcon = getIcon(category.icon)

            return (
              <Card key={category.id}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-sm">
                    <CategoryIcon className="w-4 h-4" />
                    {category.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {categoryBenefits.map(benefit => {
                    const BenefitIcon = getIcon(benefit.icon)
                    const isSelected = selectedBenefits.includes(benefit.id)

                    return (
                      <div key={benefit.id} className="flex items-center space-x-2 space-x-reverse">
                        <Checkbox
                          id={benefit.id}
                          checked={isSelected}
                          onCheckedChange={() => handleBenefitToggle(benefit.id)}
                        />
                        <div className="flex items-center gap-2 flex-1">
                          <BenefitIcon className="w-4 h-4 text-gray-500" />
                          <div className="flex-1">
                            <Label htmlFor={benefit.id} className="text-sm font-medium cursor-pointer">
                              {benefit.name}
                            </Label>
                            <p className="text-xs text-gray-500">{benefit.description}</p>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {selectedBenefits.length > 0 && (
          <div className="mt-4">
            <Label className="text-sm font-medium">المميزات المختارة:</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {selectedBenefits.map(benefitId => {
                const benefit = benefits.find(b => b.id === benefitId)
                if (!benefit) return null

                const BenefitIcon = getIcon(benefit.icon)
                return (
                  <Badge key={benefitId} variant="secondary" className="flex items-center gap-1">
                    <BenefitIcon className="w-3 h-3" />
                    {benefit.name}
                  </Badge>
                )
              })}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">إدارة المميزات الوظيفية</h3>
          <p className="text-sm text-gray-600">إدارة قائمة المميزات المتاحة للوظائف</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setEditingBenefit(null)}>
              <Plus className="w-4 h-4 ml-2" />
              إضافة ميزة جديدة
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingBenefit ? 'تعديل الميزة' : 'إضافة ميزة جديدة'}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="benefit-name">اسم الميزة</Label>
                <Input
                  id="benefit-name"
                  value={newBenefit.name}
                  onChange={(e) => setNewBenefit({ ...newBenefit, name: e.target.value })}
                  placeholder="مثال: التأمين الطبي"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="benefit-category">الفئة</Label>
                <Select
                  value={newBenefit.category}
                  onValueChange={(value) => setNewBenefit({ ...newBenefit, category: value as JobBenefit['category'] })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {benefitCategories.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="benefit-description">الوصف</Label>
                <Textarea
                  id="benefit-description"
                  value={newBenefit.description}
                  onChange={(e) => setNewBenefit({ ...newBenefit, description: e.target.value })}
                  placeholder="وصف تفصيلي للميزة..."
                />
              </div>

              <div className="flex justify-end space-x-2 space-x-reverse">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  إلغاء
                </Button>
                <Button onClick={editingBenefit ? handleUpdateBenefit : handleAddBenefit}>
                  {editingBenefit ? 'تحديث' : 'إضافة'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Rest of the management interface would go here */}
    </div>
  )
}
