"use client"

import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"

interface CandidateAnalysis {
  skills: string[]
  experience: number
  education: string
  strengths: string[]
  weaknesses: string[]
  jobMatchScore: number
  recommendations: string[]
}

interface JobRecommendation {
  jobId: string
  matchScore: number
  reasons: string[]
  candidateId: string
}

export class AIService {
  private isEnabled: boolean
  private model: string

  constructor(isEnabled = true, model = "gpt-4") {
    this.isEnabled = isEnabled
    this.model = model
  }

  async analyzeCandidateResume(resumeText: string): Promise<CandidateAnalysis | null> {
    if (!this.isEnabled) {
      return null
    }

    try {
      const { text } = await generateText({
        model: openai(this.model),
        prompt: `
          قم بتحليل السيرة الذاتية التالية واستخراج المعلومات المطلوبة:

          السيرة الذاتية:
          ${resumeText}

          يرجى تقديم التحليل في شكل JSON يحتوي على:
          - skills: قائمة بالمهارات المستخرجة
          - experience: عدد سنوات الخبرة
          - education: المؤهل التعليمي
          - strengths: نقاط القوة
          - weaknesses: نقاط الضعف المحتملة
          - jobMatchScore: درجة من 100 لمدى ملاءمة المرشح للوظائف التقنية
          - recommendations: توصيات لتحسين الملف الشخصي

          تأكد من أن الإجابة باللغة العربية وفي شكل JSON صالح.
        `,
      })

      return JSON.parse(text) as CandidateAnalysis
    } catch (error) {
      console.error("Error analyzing resume:", error)
      return null
    }
  }

  async matchJobsToCandidate(candidateProfile: any, availableJobs: any[]): Promise<JobRecommendation[]> {
    if (!this.isEnabled) {
      return []
    }

    try {
      const { text } = await generateText({
        model: openai(this.model),
        prompt: `
          بناءً على ملف المرشح التالي:
          ${JSON.stringify(candidateProfile, null, 2)}

          والوظائف المتاحة:
          ${JSON.stringify(availableJobs, null, 2)}

          قم بترتيب الوظائف حسب مدى ملاءمتها للمرشح وتقديم التوصيات.

          يرجى تقديم النتيجة في شكل JSON array يحتوي على:
          - jobId: معرف الوظيفة
          - matchScore: درجة المطابقة من 100
          - reasons: أسباب المطابقة
          - candidateId: معرف المرشح

          رتب النتائج حسب درجة المطابقة من الأعلى للأقل.
        `,
      })

      return JSON.parse(text) as JobRecommendation[]
    } catch (error) {
      console.error("Error matching jobs:", error)
      return []
    }
  }

  async generateJobDescription(jobTitle: string, requirements: string[]): Promise<string | null> {
    if (!this.isEnabled) {
      return null
    }

    try {
      const { text } = await generateText({
        model: openai(this.model),
        prompt: `
          قم بإنشاء وصف وظيفي شامل ومهني لوظيفة "${jobTitle}" بناءً على المتطلبات التالية:
          ${requirements.join("\n- ")}

          يجب أن يتضمن الوصف:
          1. نبذة عن الوظيفة
          2. المسؤوليات الرئيسية
          3. المتطلبات والمؤهلات
          4. المهارات المطلوبة
          5. المزايا والحوافز

          اكتب الوصف باللغة العربية بأسلوب مهني وجذاب.
        `,
      })

      return text
    } catch (error) {
      console.error("Error generating job description:", error)
      return null
    }
  }

  async generateInterviewQuestions(jobTitle: string, candidateProfile: any): Promise<string[] | null> {
    if (!this.isEnabled) {
      return null
    }

    try {
      const { text } = await generateText({
        model: openai(this.model),
        prompt: `
          بناءً على وظيفة "${jobTitle}" وملف المرشح التالي:
          ${JSON.stringify(candidateProfile, null, 2)}

          قم بإنشاء قائمة من 10 أسئلة مقابلة مناسبة تغطي:
          - الخبرة التقنية
          - المهارات الشخصية
          - حل المشكلات
          - التحديات المهنية
          - الأهداف المستقبلية

          قدم الأسئلة في شكل JSON array من النصوص باللغة العربية.
        `,
      })

      return JSON.parse(text) as string[]
    } catch (error) {
      console.error("Error generating interview questions:", error)
      return null
    }
  }

  async chatbotResponse(userMessage: string, context?: any): Promise<string | null> {
    if (!this.isEnabled) {
      return "عذراً، خدمة الدردشة الذكية غير متاحة حالياً."
    }

    try {
      const { text } = await generateText({
        model: openai(this.model),
        prompt: `
          أنت مساعد ذكي لشركة Yas Global Partner المتخصصة في حلول الموارد البشرية والتوظيف.
          
          السياق: ${context ? JSON.stringify(context) : "لا يوجد سياق إضافي"}
          
          رسالة المستخدم: ${userMessage}

          قم بالرد بطريقة مهنية ومفيدة باللغة العربية. إذا كان السؤال خارج نطاق الموارد البشرية والتوظيف، 
          وجه المستخدم بلطف للتواصل مع فريق الدعم.
        `,
      })

      return text
    } catch (error) {
      console.error("Error generating chatbot response:", error)
      return "عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى."
    }
  }
}

// Export singleton instance
export const aiService = new AIService()

// Hook for using AI service with settings
export function useAI() {
  // This would typically get settings from context
  const isEnabled = true // Get from settings context
  const model = "gpt-4" // Get from settings context

  return new AIService(isEnabled, model)
}
