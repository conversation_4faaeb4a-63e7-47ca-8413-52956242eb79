"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

interface LanguageContextType {
  language: string
  setLanguage: (lang: string) => void
  t: (key: string) => string
  isRTL: boolean
}

const translations = {
  ar: {
    // Navigation
    home: "الرئيسية",
    services: "الخدمات",
    about: "من نحن",
    contact: "اتصل بنا",
    dashboard: "لوحة التحكم",
    login: "تسجيل الدخول",
    logout: "تسجيل الخروج",

    // Dashboard
    clients: "العملاء",
    jobs: "الوظائف",
    candidates: "المرشحين",
    reports: "التقارير",
    settings: "الإعدادات",
    notifications: "الإشعارات",
    users: "المستخدمين",

    // Common
    save: "حفظ",
    cancel: "إلغاء",
    edit: "تعديل",
    delete: "حذف",
    add: "إضافة",
    search: "بحث",
    filter: "تصفية",
    export: "تصدير",

    // Hero Section
    heroTitle: "شريكك الموثوق في حلول الموارد البشرية",
    heroSubtitle: "نقدم خدمات التوظيف والاستشارات الإدارية المتميزة لمساعدة الشركات في العثور على أفضل المواهب",
    getStarted: "ابدأ الآن",
    learnMore: "اعرف المزيد",

    // Services
    servicesTitle: "خدماتنا",
    recruitment: "التوظيف",
    hrConsulting: "الاستشارات الإدارية",
    training: "التدريب والتطوير",

    // About
    aboutTitle: "من نحن",
    aboutDescription: "شركة رائدة في مجال الموارد البشرية والتوظيف",

    // Contact
    contactTitle: "اتصل بنا",
    phone: "الهاتف",
    email: "البريد الإلكتروني",
    address: "العنوان",
  },
  en: {
    // Navigation
    home: "Home",
    services: "Services",
    about: "About",
    contact: "Contact",
    dashboard: "Dashboard",
    login: "Login",
    logout: "Logout",

    // Dashboard
    clients: "Clients",
    jobs: "Jobs",
    candidates: "Candidates",
    reports: "Reports",
    settings: "Settings",
    notifications: "Notifications",
    users: "Users",

    // Common
    save: "Save",
    cancel: "Cancel",
    edit: "Edit",
    delete: "Delete",
    add: "Add",
    search: "Search",
    filter: "Filter",
    export: "Export",

    // Hero Section
    heroTitle: "Your Trusted Partner in HR Solutions",
    heroSubtitle:
      "We provide exceptional recruitment and HR consulting services to help companies find the best talent",
    getStarted: "Get Started",
    learnMore: "Learn More",

    // Services
    servicesTitle: "Our Services",
    recruitment: "Recruitment",
    hrConsulting: "HR Consulting",
    training: "Training & Development",

    // About
    aboutTitle: "About Us",
    aboutDescription: "Leading company in human resources and recruitment",

    // Contact
    contactTitle: "Contact Us",
    phone: "Phone",
    email: "Email",
    address: "Address",
  },
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguageState] = useState("ar")

  useEffect(() => {
    const savedLanguage = localStorage.getItem("yas-global-language") || "ar"
    setLanguageState(savedLanguage)

    // Update document direction
    document.documentElement.dir = savedLanguage === "ar" ? "rtl" : "ltr"
    document.documentElement.lang = savedLanguage
  }, [])

  const setLanguage = (lang: string) => {
    setLanguageState(lang)
    localStorage.setItem("yas-global-language", lang)

    // Update document direction
    document.documentElement.dir = lang === "ar" ? "rtl" : "ltr"
    document.documentElement.lang = lang
  }

  const t = (key: string): string => {
    return translations[language as keyof typeof translations]?.[key as keyof typeof translations.ar] || key
  }

  const isRTL = language === "ar"

  return <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>{children}</LanguageContext.Provider>
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
