"use client"

import { BenefitsManager } from "@/components/benefits-manager"

interface BenefitsSelectorProps {
  selectedBenefits: string[]
  onBenefitsChange: (benefits: string[]) => void
  maxBenefits?: number
}

export function BenefitsSelector({ selectedBenefits, onBenefitsChange, maxBenefits = 10 }: BenefitsSelectorProps) {
  return (
    <BenefitsManager
      selectedBenefits={selectedBenefits}
      onBenefitsChange={onBenefitsChange}
      mode="select"
    />
  )
}
