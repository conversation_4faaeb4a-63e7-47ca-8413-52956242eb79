"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Edit, Trash2, Search, Tag, X } from "lucide-react"

interface Skill {
  id: string
  name: string
  category: string
  level?: "beginner" | "intermediate" | "advanced" | "expert"
  description?: string
}

interface SkillCategory {
  id: string
  name: string
  color: string
}

const defaultCategories: SkillCategory[] = [
  { id: "tech", name: "تقنية", color: "bg-blue-100 text-blue-800" },
  { id: "soft", name: "مهارات شخصية", color: "bg-green-100 text-green-800" },
  { id: "language", name: "لغات", color: "bg-purple-100 text-purple-800" },
  { id: "management", name: "إدارية", color: "bg-orange-100 text-orange-800" },
  { id: "creative", name: "إبداعية", color: "bg-pink-100 text-pink-800" },
]

const defaultSkills: Skill[] = [
  { id: "1", name: "JavaScript", category: "tech", level: "advanced", description: "لغة برمجة للويب" },
  { id: "2", name: "React", category: "tech", level: "intermediate", description: "مكتبة JavaScript" },
  { id: "3", name: "Node.js", category: "tech", level: "intermediate", description: "بيئة تشغيل JavaScript" },
  { id: "4", name: "التواصل", category: "soft", level: "expert", description: "مهارات التواصل الفعال" },
  { id: "5", name: "القيادة", category: "soft", level: "advanced", description: "مهارات القيادة والإدارة" },
  { id: "6", name: "الإنجليزية", category: "language", level: "advanced", description: "اللغة الإنجليزية" },
  {
    id: "7",
    name: "إدارة المشاريع",
    category: "management",
    level: "intermediate",
    description: "إدارة وتنظيم المشاريع",
  },
  { id: "8", name: "التصميم الجرافيكي", category: "creative", level: "beginner", description: "تصميم المواد البصرية" },
]

interface SkillsSelectorProps {
  selectedSkills?: string[]
  onSkillsChange?: (skills: string[]) => void
  maxSkills?: number
  allowCustomSkills?: boolean
}

export function SkillsSelector({
  selectedSkills = [],
  onSkillsChange,
  maxSkills = 10,
  allowCustomSkills = true,
}: SkillsSelectorProps) {
  const [skills, setSkills] = useState<Skill[]>(defaultSkills)
  const [categories, setCategories] = useState<SkillCategory[]>(defaultCategories)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [isAddSkillOpen, setIsAddSkillOpen] = useState(false)
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false)
  const [editingSkill, setEditingSkill] = useState<Skill | null>(null)
  const [editingCategory, setEditingCategory] = useState<SkillCategory | null>(null)

  const [newSkill, setNewSkill] = useState({
    name: "",
    category: "",
    level: "intermediate" as Skill["level"],
    description: "",
  })

  const [newCategory, setNewCategory] = useState({
    name: "",
    color: "bg-gray-100 text-gray-800",
  })

  // Load skills from localStorage
  useEffect(() => {
    const savedSkills = localStorage.getItem("yas-global-skills")
    const savedCategories = localStorage.getItem("yas-global-skill-categories")

    if (savedSkills) {
      try {
        setSkills(JSON.parse(savedSkills))
      } catch (error) {
        console.error("Error loading skills:", error)
      }
    }

    if (savedCategories) {
      try {
        setCategories(JSON.parse(savedCategories))
      } catch (error) {
        console.error("Error loading categories:", error)
      }
    }
  }, [])

  // Save skills to localStorage
  const saveSkills = (newSkills: Skill[]) => {
    setSkills(newSkills)
    localStorage.setItem("yas-global-skills", JSON.stringify(newSkills))
  }

  const saveCategories = (newCategories: SkillCategory[]) => {
    setCategories(newCategories)
    localStorage.setItem("yas-global-skill-categories", JSON.stringify(newCategories))
  }

  const filteredSkills = skills.filter((skill) => {
    const matchesSearch = skill.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || skill.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleSkillToggle = (skillId: string) => {
    const newSelectedSkills = selectedSkills.includes(skillId)
      ? selectedSkills.filter((id) => id !== skillId)
      : selectedSkills.length < maxSkills
        ? [...selectedSkills, skillId]
        : selectedSkills

    onSkillsChange?.(newSelectedSkills)
  }

  const handleAddSkill = () => {
    if (!newSkill.name.trim() || !newSkill.category) return

    const skill: Skill = {
      id: Date.now().toString(),
      name: newSkill.name.trim(),
      category: newSkill.category,
      level: newSkill.level,
      description: newSkill.description.trim() || undefined,
    }

    saveSkills([...skills, skill])
    setNewSkill({ name: "", category: "", level: "intermediate", description: "" })
    setIsAddSkillOpen(false)
  }

  const handleEditSkill = (skill: Skill) => {
    setEditingSkill(skill)
    setNewSkill({
      name: skill.name,
      category: skill.category,
      level: skill.level || "intermediate",
      description: skill.description || "",
    })
  }

  const handleUpdateSkill = () => {
    if (!editingSkill || !newSkill.name.trim()) return

    const updatedSkills = skills.map((skill) =>
      skill.id === editingSkill.id
        ? {
            ...skill,
            name: newSkill.name.trim(),
            category: newSkill.category,
            level: newSkill.level,
            description: newSkill.description.trim() || undefined,
          }
        : skill,
    )

    saveSkills(updatedSkills)
    setEditingSkill(null)
    setNewSkill({ name: "", category: "", level: "intermediate", description: "" })
  }

  const handleDeleteSkill = (skillId: string) => {
    if (confirm("هل أنت متأكد من حذف هذه المهارة؟")) {
      saveSkills(skills.filter((skill) => skill.id !== skillId))
      // Remove from selected skills if it was selected
      if (selectedSkills.includes(skillId)) {
        onSkillsChange?.(selectedSkills.filter((id) => id !== skillId))
      }
    }
  }

  const handleAddCategory = () => {
    if (!newCategory.name.trim()) return

    const category: SkillCategory = {
      id: Date.now().toString(),
      name: newCategory.name.trim(),
      color: newCategory.color,
    }

    saveCategories([...categories, category])
    setNewCategory({ name: "", color: "bg-gray-100 text-gray-800" })
    setIsAddCategoryOpen(false)
  }

  const handleEditCategory = (category: SkillCategory) => {
    setEditingCategory(category)
    setNewCategory({
      name: category.name,
      color: category.color,
    })
  }

  const handleUpdateCategory = () => {
    if (!editingCategory || !newCategory.name.trim()) return

    const updatedCategories = categories.map((cat) =>
      cat.id === editingCategory.id
        ? {
            ...cat,
            name: newCategory.name.trim(),
            color: newCategory.color,
          }
        : cat,
    )

    saveCategories(updatedCategories)
    setEditingCategory(null)
    setNewCategory({ name: "", color: "bg-gray-100 text-gray-800" })
  }

  const handleDeleteCategory = (categoryId: string) => {
    if (confirm("هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع المهارات المرتبطة بها.")) {
      saveCategories(categories.filter((cat) => cat.id !== categoryId))
      saveSkills(skills.filter((skill) => skill.category !== categoryId))
    }
  }

  const getCategoryName = (categoryId: string) => {
    return categories.find((cat) => cat.id === categoryId)?.name || categoryId
  }

  const getCategoryColor = (categoryId: string) => {
    return categories.find((cat) => cat.id === categoryId)?.color || "bg-gray-100 text-gray-800"
  }

  const getLevelName = (level?: string) => {
    switch (level) {
      case "beginner":
        return "مبتدئ"
      case "intermediate":
        return "متوسط"
      case "advanced":
        return "متقدم"
      case "expert":
        return "خبير"
      default:
        return "غير محدد"
    }
  }

  const colorOptions = [
    { value: "bg-blue-100 text-blue-800", label: "أزرق" },
    { value: "bg-green-100 text-green-800", label: "أخضر" },
    { value: "bg-purple-100 text-purple-800", label: "بنفسجي" },
    { value: "bg-orange-100 text-orange-800", label: "برتقالي" },
    { value: "bg-pink-100 text-pink-800", label: "وردي" },
    { value: "bg-red-100 text-red-800", label: "أحمر" },
    { value: "bg-yellow-100 text-yellow-800", label: "أصفر" },
    { value: "bg-gray-100 text-gray-800", label: "رمادي" },
  ]

  return (
    <div className="space-y-6">
      {/* Selected Skills */}
      {selectedSkills.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">
              المهارات المختارة ({selectedSkills.length}/{maxSkills})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {selectedSkills.map((skillId) => {
                const skill = skills.find((s) => s.id === skillId)
                if (!skill) return null
                return (
                  <Badge
                    key={skillId}
                    className={`${getCategoryColor(skill.category)} cursor-pointer`}
                    onClick={() => handleSkillToggle(skillId)}
                  >
                    {skill.name}
                    <X className="h-3 w-3 ml-1" />
                  </Badge>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Skills Management */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              إدارة المهارات
            </CardTitle>
            <div className="flex gap-2">
              {allowCustomSkills && (
                <>
                  <Dialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        فئة جديدة
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>إضافة فئة جديدة</DialogTitle>
                        <DialogDescription>أضف فئة جديدة لتصنيف المهارات</DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="category-name">اسم الفئة</Label>
                          <Input
                            id="category-name"
                            value={newCategory.name}
                            onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                            placeholder="أدخل اسم الفئة"
                          />
                        </div>
                        <div>
                          <Label htmlFor="category-color">اللون</Label>
                          <Select
                            value={newCategory.color}
                            onValueChange={(value) => setNewCategory({ ...newCategory, color: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {colorOptions.map((color) => (
                                <SelectItem key={color.value} value={color.value}>
                                  <div className="flex items-center gap-2">
                                    <div className={`w-4 h-4 rounded ${color.value}`}></div>
                                    {color.label}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={handleAddCategory} className="flex-1">
                            إضافة
                          </Button>
                          <Button variant="outline" onClick={() => setIsAddCategoryOpen(false)} className="flex-1">
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  <Dialog open={isAddSkillOpen} onOpenChange={setIsAddSkillOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        مهارة جديدة
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>إضافة مهارة جديدة</DialogTitle>
                        <DialogDescription>أضف مهارة جديدة إلى قاعدة البيانات</DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="skill-name">اسم المهارة</Label>
                          <Input
                            id="skill-name"
                            value={newSkill.name}
                            onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
                            placeholder="أدخل اسم المهارة"
                          />
                        </div>
                        <div>
                          <Label htmlFor="skill-category">الفئة</Label>
                          <Select
                            value={newSkill.category}
                            onValueChange={(value) => setNewSkill({ ...newSkill, category: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="اختر الفئة" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id}>
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="skill-level">المستوى</Label>
                          <Select
                            value={newSkill.level}
                            onValueChange={(value: any) => setNewSkill({ ...newSkill, level: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">مبتدئ</SelectItem>
                              <SelectItem value="intermediate">متوسط</SelectItem>
                              <SelectItem value="advanced">متقدم</SelectItem>
                              <SelectItem value="expert">خبير</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="skill-description">الوصف (اختياري)</Label>
                          <Input
                            id="skill-description"
                            value={newSkill.description}
                            onChange={(e) => setNewSkill({ ...newSkill, description: e.target.value })}
                            placeholder="وصف المهارة"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={handleAddSkill} className="flex-1">
                            إضافة
                          </Button>
                          <Button variant="outline" onClick={() => setIsAddSkillOpen(false)} className="flex-1">
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </>
              )}
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المهارات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="تصفية حسب الفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفئات</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent>
          {/* Categories Management */}
          <div className="mb-6">
            <h4 className="font-medium mb-3">الفئات</h4>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center gap-1">
                  <Badge className={category.color}>{category.name}</Badge>
                  {allowCustomSkills && (
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => handleEditCategory(category)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-red-600"
                        onClick={() => handleDeleteCategory(category.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Skills Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {filteredSkills.map((skill) => (
              <div
                key={skill.id}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedSkills.includes(skill.id)
                    ? "border-primary bg-primary/5"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => handleSkillToggle(skill.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-sm">{skill.name}</h4>
                  {allowCustomSkills && (
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditSkill(skill)
                        }}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-red-600"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteSkill(skill.id)
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
                <div className="space-y-1">
                  <Badge className={getCategoryColor(skill.category)} variant="secondary">
                    {getCategoryName(skill.category)}
                  </Badge>
                  {skill.level && <div className="text-xs text-gray-600">المستوى: {getLevelName(skill.level)}</div>}
                  {skill.description && <div className="text-xs text-gray-600">{skill.description}</div>}
                </div>
              </div>
            ))}
          </div>

          {filteredSkills.length === 0 && (
            <div className="text-center py-8">
              <Tag className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">لا توجد مهارات مطابقة للبحث</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Skill Dialog */}
      <Dialog open={!!editingSkill} onOpenChange={() => setEditingSkill(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل المهارة</DialogTitle>
            <DialogDescription>تعديل بيانات المهارة</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-skill-name">اسم المهارة</Label>
              <Input
                id="edit-skill-name"
                value={newSkill.name}
                onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="edit-skill-category">الفئة</Label>
              <Select
                value={newSkill.category}
                onValueChange={(value) => setNewSkill({ ...newSkill, category: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="edit-skill-level">المستوى</Label>
              <Select value={newSkill.level} onValueChange={(value: any) => setNewSkill({ ...newSkill, level: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">مبتدئ</SelectItem>
                  <SelectItem value="intermediate">متوسط</SelectItem>
                  <SelectItem value="advanced">متقدم</SelectItem>
                  <SelectItem value="expert">خبير</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="edit-skill-description">الوصف</Label>
              <Input
                id="edit-skill-description"
                value={newSkill.description}
                onChange={(e) => setNewSkill({ ...newSkill, description: e.target.value })}
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleUpdateSkill} className="flex-1">
                حفظ التغييرات
              </Button>
              <Button variant="outline" onClick={() => setEditingSkill(null)} className="flex-1">
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog open={!!editingCategory} onOpenChange={() => setEditingCategory(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل الفئة</DialogTitle>
            <DialogDescription>تعديل بيانات الفئة</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-category-name">اسم الفئة</Label>
              <Input
                id="edit-category-name"
                value={newCategory.name}
                onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="edit-category-color">اللون</Label>
              <Select
                value={newCategory.color}
                onValueChange={(value) => setNewCategory({ ...newCategory, color: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {colorOptions.map((color) => (
                    <SelectItem key={color.value} value={color.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-4 h-4 rounded ${color.value}`}></div>
                        {color.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleUpdateCategory} className="flex-1">
                حفظ التغييرات
              </Button>
              <Button variant="outline" onClick={() => setEditingCategory(null)} className="flex-1">
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
