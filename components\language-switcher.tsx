"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Globe } from "lucide-react"
import { useLanguage } from "@/hooks/use-language"

export function LanguageSwitcher() {
  const { currentLanguage, toggleLanguage, t } = useLanguage()

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="flex items-center space-x-2 rtl:space-x-reverse"
    >
      <Globe className="h-4 w-4" />
      <span className="text-sm">{currentLanguage === "ar" ? "EN" : "عربي"}</span>
    </Button>
  )
}
