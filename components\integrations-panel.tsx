"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Settings,
  Link,
  CheckCircle,
  XCircle,
  AlertCircle,
  Linkedin,
  MessageSquare,
  Calendar,
  Zap,
  Globe,
  Key,
  RefreshCw,
} from "lucide-react"

interface Integration {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  status: "connected" | "disconnected" | "error"
  apiKey?: string
  enabled: boolean
  lastSync?: string
  features: string[]
}

export function IntegrationsPanel() {
  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: "workable",
      name: "Workable API",
      description: "استيراد الوظائف والمرشحين تلقائياً من Workable",
      icon: <Globe className="h-5 w-5" />,
      status: "disconnected",
      enabled: false,
      features: ["استيراد الوظائف", "مزامنة المرشحين", "تحديث الحالات"],
    },
    {
      id: "linkedin",
      name: "LinkedIn API",
      description: "البحث عن المرشحين ونشر الوظائف على LinkedIn",
      icon: <Linkedin className="h-5 w-5" />,
      status: "connected",
      enabled: true,
      lastSync: "منذ ساعة",
      features: ["نشر الوظائف", "البحث عن المرشحين", "استيراد الملفات الشخصية"],
    },
    {
      id: "whatsapp",
      name: "WhatsApp Cloud API",
      description: "إرسال الإشعارات والرسائل عبر WhatsApp",
      icon: <MessageSquare className="h-5 w-5" />,
      status: "connected",
      enabled: true,
      lastSync: "منذ 30 دقيقة",
      features: ["إشعارات المقابلات", "تحديثات الحالة", "رسائل ترحيبية"],
    },
    {
      id: "google-calendar",
      name: "Google Calendar",
      description: "جدولة المقابلات ومزامنة التقويم",
      icon: <Calendar className="h-5 w-5" />,
      status: "error",
      enabled: false,
      features: ["جدولة المقابلات", "تذكيرات تلقائية", "مزامنة التقويم"],
    },
    {
      id: "zapier",
      name: "Zapier",
      description: "ربط النظام مع أكثر من 3000 تطبيق",
      icon: <Zap className="h-5 w-5" />,
      status: "disconnected",
      enabled: false,
      features: ["أتمتة المهام", "ربط التطبيقات", "تدفق البيانات"],
    },
  ])

  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null)
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false)
  const [apiKey, setApiKey] = useState("")

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "connected":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "connected":
        return "bg-green-100 text-green-800"
      case "error":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "connected":
        return "متصل"
      case "error":
        return "خطأ"
      default:
        return "غير متصل"
    }
  }

  const handleToggleIntegration = (id: string) => {
    setIntegrations(
      integrations.map((integration) =>
        integration.id === id ? { ...integration, enabled: !integration.enabled } : integration,
      ),
    )
  }

  const handleConfigureIntegration = (integration: Integration) => {
    setSelectedIntegration(integration)
    setApiKey(integration.apiKey || "")
    setIsConfigDialogOpen(true)
  }

  const handleSaveConfiguration = () => {
    if (selectedIntegration) {
      setIntegrations(
        integrations.map((integration) =>
          integration.id === selectedIntegration.id
            ? {
                ...integration,
                apiKey,
                status: apiKey ? "connected" : "disconnected",
                enabled: !!apiKey,
                lastSync: apiKey ? "الآن" : undefined,
              }
            : integration,
        ),
      )
    }
    setIsConfigDialogOpen(false)
    setSelectedIntegration(null)
    setApiKey("")
  }

  const handleTestConnection = async (integration: Integration) => {
    // Simulate API test
    setIntegrations(
      integrations.map((int) => (int.id === integration.id ? { ...int, status: "connected", lastSync: "الآن" } : int)),
    )
  }

  const connectedCount = integrations.filter((i) => i.status === "connected").length
  const enabledCount = integrations.filter((i) => i.enabled).length

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">إجمالي التكاملات</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{integrations.length}</p>
              </div>
              <Link className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">متصلة</p>
                <p className="text-2xl font-bold text-green-600">{connectedCount}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">مفعلة</p>
                <p className="text-2xl font-bold text-blue-600">{enabledCount}</p>
              </div>
              <Settings className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Integrations List */}
      <Card>
        <CardHeader>
          <CardTitle>التكاملات المتاحة</CardTitle>
          <CardDescription>قم بربط النظام مع الخدمات الخارجية لتحسين الأداء</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {integrations.map((integration) => (
              <div key={integration.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">{integration.icon}</div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">{integration.name}</h3>
                        <Badge className={getStatusColor(integration.status)}>
                          {getStatusIcon(integration.status)}
                          <span className="mr-1">{getStatusText(integration.status)}</span>
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{integration.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {integration.features.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                      {integration.lastSync && (
                        <p className="text-xs text-gray-500 mt-2">آخر مزامنة: {integration.lastSync}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Switch
                      checked={integration.enabled}
                      onCheckedChange={() => handleToggleIntegration(integration.id)}
                      disabled={integration.status === "disconnected"}
                    />
                    <Button variant="outline" size="sm" onClick={() => handleConfigureIntegration(integration)}>
                      <Settings className="h-4 w-4" />
                    </Button>
                    {integration.status === "connected" && (
                      <Button variant="outline" size="sm" onClick={() => handleTestConnection(integration)}>
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Configuration Dialog */}
      <Dialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedIntegration?.icon}
              إعداد {selectedIntegration?.name}
            </DialogTitle>
            <DialogDescription>{selectedIntegration?.description}</DialogDescription>
          </DialogHeader>

          {selectedIntegration && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="apiKey" className="flex items-center gap-2">
                    <Key className="h-4 w-4" />
                    API Key
                  </Label>
                  <Input
                    id="apiKey"
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="أدخل API Key الخاص بك"
                  />
                  <p className="text-xs text-gray-500">
                    يمكنك الحصول على API Key من لوحة تحكم {selectedIntegration.name}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>الميزات المتاحة</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedIntegration.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2 rtl:space-x-reverse">
                <Button variant="outline" onClick={() => setIsConfigDialogOpen(false)}>
                  إلغاء
                </Button>
                <Button onClick={handleSaveConfiguration} className="bg-[#C0322D] hover:bg-[#C1352F]">
                  حفظ الإعدادات
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
