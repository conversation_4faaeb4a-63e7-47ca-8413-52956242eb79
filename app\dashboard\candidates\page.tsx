"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import {
  Plus,
  Search,
  Edit,
  Eye,
  Phone,
  Mail,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Download,
} from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import * as XLSX from "xlsx"

interface CandidateTimeline {
  id: string
  stage:
    | "الترشح"
    | "المقابلة الأولى"
    | "قبول/رفض"
    | "المقابلة مع العميل"
    | "عرض العمل"
    | "قبول العرض"
    | "فترة الضمان"
    | "مكتمل"
  status: "مكتمل" | "قيد التنفيذ" | "معلق" | "مرفوض"
  date: string
  notes: string
  nextAction?: string
}

interface Candidate {
  id: string
  name: string
  email: string
  phone: string
  position: string
  experience: string
  skills: string[]
  status:
    | "جديد"
    | "تحت المراجعة"
    | "مقابلة أولى"
    | "مقابلة مع العميل"
    | "عرض مرسل"
    | "مقبول"
    | "مرفوض"
    | "موظف"
    | "فترة ضمان"
  appliedDate: string
  jobId: string
  jobTitle: string
  resumeUrl?: string
  timeline: CandidateTimeline[]
  guaranteePeriod?: {
    startDate: string
    endDate: string
    status: "نشط" | "منتهي" | "ناجح"
  }
}

export default function CandidatesPage() {
  const [candidates, setCandidates] = useState<Candidate[]>([
    {
      id: "1",
      name: "أحمد محمد علي",
      email: "<EMAIL>",
      phone: "+966501234567",
      position: "مطور React Senior",
      experience: "5 سنوات",
      skills: ["React", "JavaScript", "Node.js", "MongoDB"],
      status: "فترة ضمان",
      appliedDate: "2024-01-15",
      jobId: "1",
      jobTitle: "مطور React Senior",
      timeline: [
        { id: "1", stage: "الترشح", status: "مكتمل", date: "2024-01-15", notes: "تم استلام الطلب" },
        { id: "2", stage: "المقابلة الأولى", status: "مكتمل", date: "2024-01-20", notes: "مقابلة ناجحة، مرشح ممتاز" },
        { id: "3", stage: "قبول/رفض", status: "مكتمل", date: "2024-01-22", notes: "تم قبول المرشح للمرحلة التالية" },
        { id: "4", stage: "المقابلة مع العميل", status: "مكتمل", date: "2024-01-25", notes: "العميل راضي عن المرشح" },
        { id: "5", stage: "عرض العمل", status: "مكتمل", date: "2024-01-28", notes: "تم إرسال العرض" },
        { id: "6", stage: "قبول العرض", status: "مكتمل", date: "2024-01-30", notes: "المرشح قبل العرض" },
        { id: "7", stage: "فترة الضمان", status: "قيد التنفيذ", date: "2024-02-01", notes: "بدء فترة الضمان 3 أشهر" },
      ],
      guaranteePeriod: {
        startDate: "2024-02-01",
        endDate: "2024-05-01",
        status: "نشط",
      },
    },
    {
      id: "2",
      name: "فاطمة أحمد",
      email: "<EMAIL>",
      phone: "+966507654321",
      position: "مدير مشاريع",
      experience: "7 سنوات",
      skills: ["إدارة المشاريع", "Agile", "Scrum", "Leadership"],
      status: "مقابلة مع العميل",
      appliedDate: "2024-02-01",
      jobId: "2",
      jobTitle: "مدير مشاريع",
      timeline: [
        { id: "1", stage: "الترشح", status: "مكتمل", date: "2024-02-01", notes: "تم استلام الطلب" },
        { id: "2", stage: "المقابلة الأولى", status: "مكتمل", date: "2024-02-05", notes: "مقابلة ممتازة" },
        { id: "3", stage: "قبول/رفض", status: "مكتمل", date: "2024-02-07", notes: "تم قبول المرشح" },
        {
          id: "4",
          stage: "المقابلة مع العميل",
          status: "قيد التنفيذ",
          date: "2024-02-10",
          notes: "مجدولة غداً",
          nextAction: "متابعة نتيجة المقابلة",
        },
      ],
    },
    {
      id: "3",
      name: "خالد السعد",
      email: "<EMAIL>",
      phone: "+966509876543",
      position: "مهندس مدني",
      experience: "3 سنوات",
      skills: ["AutoCAD", "Civil 3D", "Project Management"],
      status: "مرفوض",
      appliedDate: "2024-02-05",
      jobId: "3",
      jobTitle: "مهندس مدني",
      timeline: [
        { id: "1", stage: "الترشح", status: "مكتمل", date: "2024-02-05", notes: "تم استلام الطلب" },
        { id: "2", stage: "المقابلة الأولى", status: "مكتمل", date: "2024-02-08", notes: "المقابلة لم تكن مرضية" },
        {
          id: "3",
          stage: "قبول/رفض",
          status: "مرفوض",
          date: "2024-02-09",
          notes: "تم رفض المرشح - نقص في الخبرة المطلوبة",
        },
      ],
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null)
  const [isTimelineDialogOpen, setIsTimelineDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const filteredCandidates = candidates.filter(
    (candidate) =>
      candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.jobTitle.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const exportToExcel = () => {
    const exportData = filteredCandidates.map((candidate) => ({
      الاسم: candidate.name,
      "البريد الإلكتروني": candidate.email,
      "رقم الهاتف": candidate.phone,
      المنصب: candidate.position,
      الخبرة: candidate.experience,
      المهارات: candidate.skills.join(", "),
      الحالة: candidate.status,
      "تاريخ التقديم": candidate.appliedDate,
      "الوظيفة المتقدم لها": candidate.jobTitle,
      "مراحل مكتملة": candidate.timeline.filter((t) => t.status === "مكتمل").length,
      "إجمالي المراحل": candidate.timeline.length,
      "فترة الضمان": candidate.guaranteePeriod ? candidate.guaranteePeriod.status : "غير متاح",
    }))

    const ws = XLSX.utils.json_to_sheet(exportData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, "المرشحين")

    // Apply styling
    const range = XLSX.utils.decode_range(ws["!ref"] || "A1")
    for (let R = range.s.r; R <= range.e.r; ++R) {
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cell_address = XLSX.utils.encode_cell({ c: C, r: R })
        if (!ws[cell_address]) continue

        if (R === 0) {
          // Header row styling
          ws[cell_address].s = {
            fill: { fgColor: { rgb: "C0322D" } },
            font: { color: { rgb: "FFFFFF" }, bold: true },
            alignment: { horizontal: "center" },
          }
        }
      }
    }

    const fileName = `تقرير_المرشحين_${new Date().toISOString().split("T")[0]}.xlsx`
    XLSX.writeFile(wb, fileName)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "جديد":
        return "bg-blue-100 text-blue-800"
      case "تحت المراجعة":
        return "bg-yellow-100 text-yellow-800"
      case "مقابلة أولى":
        return "bg-purple-100 text-purple-800"
      case "مقابلة مع العميل":
        return "bg-orange-100 text-orange-800"
      case "عرض مرسل":
        return "bg-cyan-100 text-cyan-800"
      case "مقبول":
        return "bg-green-100 text-green-800"
      case "مرفوض":
        return "bg-red-100 text-red-800"
      case "موظف":
        return "bg-emerald-100 text-emerald-800"
      case "فترة ضمان":
        return "bg-indigo-100 text-indigo-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTimelineStageIcon = (stage: string, status: string) => {
    if (status === "مكتمل") return <CheckCircle className="h-5 w-5 text-green-600" />
    if (status === "مرفوض") return <XCircle className="h-5 w-5 text-red-600" />
    if (status === "قيد التنفيذ") return <Clock className="h-5 w-5 text-blue-600" />
    return <AlertCircle className="h-5 w-5 text-gray-400" />
  }

  const getTimelineProgress = (timeline: CandidateTimeline[]) => {
    const totalStages = 7 // إجمالي المراحل
    const completedStages = timeline.filter((t) => t.status === "مكتمل").length
    return (completedStages / totalStages) * 100
  }

  const openTimelineDialog = (candidate: Candidate) => {
    setSelectedCandidate(candidate)
    setIsTimelineDialogOpen(true)
  }

  const openEditDialog = (candidate: Candidate) => {
    setSelectedCandidate(candidate)
    setIsEditDialogOpen(true)
  }

  const handleResumeView = (candidate: Candidate) => {
    if (candidate.resumeUrl) {
      window.open(candidate.resumeUrl, "_blank")
    } else {
      alert("لا توجد سيرة ذاتية متاحة لهذا المرشح")
    }
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">إدارة المرشحين</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">تتبع جميع المرشحين ومراحل تقدمهم</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToExcel} className="bg-transparent">
              <Download className="h-4 w-4 mr-2" />
              تصدير Excel
            </Button>
            <Button className="bg-[#C0322D] hover:bg-[#C1352F]">
              <Plus className="h-4 w-4 mr-2" />
              إضافة مرشح جديد
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>قائمة المرشحين</CardTitle>
            <CardDescription>جميع المرشحين المسجلين في النظام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 rtl:space-x-reverse mb-6">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في المرشحين..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>

            {/* Desktop Table */}
            <div className="hidden md:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>المرشح</TableHead>
                    <TableHead>الوظيفة</TableHead>
                    <TableHead>الخبرة</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>التقدم</TableHead>
                    <TableHead>تاريخ التقديم</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCandidates.map((candidate) => (
                    <TableRow key={candidate.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{candidate.name}</div>
                          <div className="text-sm text-gray-500 space-y-1">
                            <div className="flex items-center">
                              <Mail className="h-3 w-3 mr-1" />
                              {candidate.email}
                            </div>
                            <div className="flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {candidate.phone}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{candidate.jobTitle}</div>
                        <div className="text-sm text-gray-500">{candidate.position}</div>
                      </TableCell>
                      <TableCell>
                        <div>{candidate.experience}</div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {candidate.skills.slice(0, 2).map((skill, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                          {candidate.skills.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{candidate.skills.length - 2}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(candidate.status)}>{candidate.status}</Badge>
                        {candidate.guaranteePeriod && candidate.guaranteePeriod.status === "نشط" && (
                          <div className="text-xs text-gray-500 mt-1">
                            ضمان حتى: {candidate.guaranteePeriod.endDate}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-2">
                          <Progress value={getTimelineProgress(candidate.timeline)} className="h-2" />
                          <div className="text-xs text-gray-500">
                            {candidate.timeline.filter((t) => t.status === "مكتمل").length} من 7 مراحل
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{candidate.appliedDate}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2 rtl:space-x-reverse">
                          <Button variant="ghost" size="sm" onClick={() => openTimelineDialog(candidate)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => openEditDialog(candidate)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => handleResumeView(candidate)}>
                            <FileText className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {filteredCandidates.map((candidate) => (
                <Card key={candidate.id}>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{candidate.name}</h3>
                          <p className="text-sm text-gray-600">{candidate.jobTitle}</p>
                          <div className="text-sm text-gray-500 space-y-1 mt-1">
                            <div className="flex items-center">
                              <Mail className="h-3 w-3 mr-1" />
                              {candidate.email}
                            </div>
                            <div className="flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {candidate.phone}
                            </div>
                          </div>
                        </div>
                        <Badge className={getStatusColor(candidate.status)}>{candidate.status}</Badge>
                      </div>

                      <div className="flex flex-wrap gap-1">
                        {candidate.skills.slice(0, 3).map((skill, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                        {candidate.skills.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{candidate.skills.length - 3}
                          </Badge>
                        )}
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>التقدم</span>
                          <span>{Math.round(getTimelineProgress(candidate.timeline))}%</span>
                        </div>
                        <Progress value={getTimelineProgress(candidate.timeline)} className="h-2" />
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 bg-transparent"
                          onClick={() => openTimelineDialog(candidate)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          عرض التفاصيل
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => openEditDialog(candidate)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleResumeView(candidate)}>
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Timeline Dialog */}
        <Dialog open={isTimelineDialogOpen} onOpenChange={setIsTimelineDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>تتبع مراحل المرشح: {selectedCandidate?.name}</DialogTitle>
              <DialogDescription>
                الوظيفة: {selectedCandidate?.jobTitle} | الحالة: {selectedCandidate?.status}
              </DialogDescription>
            </DialogHeader>

            {selectedCandidate && (
              <div className="space-y-6">
                {/* Candidate Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">معلومات الاتصال</Label>
                    <div className="space-y-1 mt-1">
                      <div className="flex items-center text-sm">
                        <Mail className="h-3 w-3 mr-2" />
                        {selectedCandidate.email}
                      </div>
                      <div className="flex items-center text-sm">
                        <Phone className="h-3 w-3 mr-2" />
                        {selectedCandidate.phone}
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">الخبرة والمهارات</Label>
                    <div className="mt-1">
                      <div className="text-sm font-medium">{selectedCandidate.experience}</div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {selectedCandidate.skills.map((skill, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">التقدم العام</Label>
                    <div className="mt-1">
                      <Progress value={getTimelineProgress(selectedCandidate.timeline)} className="h-3" />
                      <div className="text-sm text-gray-600 mt-1">
                        {selectedCandidate.timeline.filter((t) => t.status === "مكتمل").length} من 7 مراحل مكتملة
                      </div>
                    </div>
                  </div>
                </div>

                {/* Timeline */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">مراحل التوظيف</h3>
                  <div className="space-y-4">
                    {selectedCandidate.timeline.map((stage, index) => (
                      <div key={stage.id} className="flex items-start space-x-4 rtl:space-x-reverse">
                        <div className="flex-shrink-0 mt-1">{getTimelineStageIcon(stage.stage, stage.status)}</div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white">{stage.stage}</h4>
                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              <Badge
                                variant="outline"
                                className={
                                  stage.status === "مكتمل"
                                    ? "bg-green-100 text-green-800"
                                    : stage.status === "قيد التنفيذ"
                                      ? "bg-blue-100 text-blue-800"
                                      : stage.status === "مرفوض"
                                        ? "bg-red-100 text-red-800"
                                        : "bg-gray-100 text-gray-800"
                                }
                              >
                                {stage.status}
                              </Badge>
                              <span className="text-sm text-gray-500">{stage.date}</span>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{stage.notes}</p>
                          {stage.nextAction && (
                            <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                              <p className="text-sm text-blue-800 dark:text-blue-200">
                                <strong>الإجراء التالي:</strong> {stage.nextAction}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Guarantee Period */}
                {selectedCandidate.guaranteePeriod && (
                  <div className="p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                    <h3 className="text-lg font-semibold text-indigo-900 dark:text-indigo-100 mb-2">فترة الضمان</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
                          تاريخ البداية
                        </Label>
                        <div className="text-sm">{selectedCandidate.guaranteePeriod.startDate}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
                          تاريخ الانتهاء
                        </Label>
                        <div className="text-sm">{selectedCandidate.guaranteePeriod.endDate}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-indigo-700 dark:text-indigo-300">الحالة</Label>
                        <Badge
                          className={
                            selectedCandidate.guaranteePeriod.status === "نشط"
                              ? "bg-green-100 text-green-800"
                              : selectedCandidate.guaranteePeriod.status === "ناجح"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                          }
                        >
                          {selectedCandidate.guaranteePeriod.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>تعديل بيانات المرشح</DialogTitle>
              <DialogDescription>تحديث معلومات المرشح وحالته</DialogDescription>
            </DialogHeader>
            {selectedCandidate && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">الاسم</Label>
                    <Input id="name" defaultValue={selectedCandidate.name} />
                  </div>
                  <div>
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input id="email" type="email" defaultValue={selectedCandidate.email} />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input id="phone" defaultValue={selectedCandidate.phone} />
                  </div>
                  <div>
                    <Label htmlFor="experience">سنوات الخبرة</Label>
                    <Input id="experience" defaultValue={selectedCandidate.experience} />
                  </div>
                </div>
                <div className="flex gap-2 pt-4">
                  <Button className="flex-1">حفظ التغييرات</Button>
                  <Button
                    variant="outline"
                    className="flex-1 bg-transparent"
                    onClick={() => setIsEditDialogOpen(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
