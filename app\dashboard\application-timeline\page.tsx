"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Calendar,
  User,
  Building,
  Phone,
  Mail,
  MessageSquare,
  Edit,
  Plus,
  Eye,
  FileText,
  Send
} from "lucide-react"

export default function ApplicationTimelinePage() {
  const [selectedApplication, setSelectedApplication] = useState<any>(null)
  const [isEditTimelineOpen, setIsEditTimelineOpen] = useState(false)

  // بيانات وهمية للطلبات
  const applications = [
    {
      id: 1,
      candidateName: "أحمد محمد السالم",
      candidateEmail: "<EMAIL>",
      candidatePhone: "+966 50 123 4567",
      jobTitle: "مهندس بترول أول",
      company: "أرامكو السعودية",
      appliedDate: "2024-01-15",
      currentStage: "interview_2",
      timeline: [
        {
          stage: "application",
          title: "تقديم الطلب",
          date: "2024-01-15",
          status: "completed",
          description: "تم تقديم الطلب بنجاح",
          notes: "السيرة الذاتية مطابقة للمتطلبات"
        },
        {
          stage: "screening",
          title: "المراجعة الأولية",
          date: "2024-01-18",
          status: "completed",
          description: "تمت مراجعة الطلب والموافقة عليه",
          notes: "مؤهلات ممتازة"
        },
        {
          stage: "interview_1",
          title: "المقابلة الأولى",
          date: "2024-01-22",
          status: "completed",
          description: "مقابلة مع قسم الموارد البشرية",
          notes: "أداء جيد جداً"
        },
        {
          stage: "interview_2",
          title: "المقابلة الثانية",
          date: "2024-01-25",
          status: "in_progress",
          description: "مقابلة مع المدير المباشر",
          notes: "مجدولة في 25 يناير"
        },
        {
          stage: "offer",
          title: "عرض العمل",
          date: null,
          status: "pending",
          description: "إرسال عرض العمل",
          notes: ""
        },
        {
          stage: "contract",
          title: "توقيع العقد",
          date: null,
          status: "pending",
          description: "توقيع عقد العمل",
          notes: ""
        },
        {
          stage: "guarantee",
          title: "فترة الضمان",
          date: null,
          status: "pending",
          description: "بداية فترة الضمان (3 أشهر)",
          notes: ""
        }
      ]
    },
    {
      id: 2,
      candidateName: "فاطمة عبدالله النمر",
      candidateEmail: "<EMAIL>",
      candidatePhone: "+966 55 987 6543",
      jobTitle: "محلل مالي",
      company: "البنك الأهلي",
      appliedDate: "2024-01-20",
      currentStage: "offer",
      timeline: [
        {
          stage: "application",
          title: "تقديم الطلب",
          date: "2024-01-20",
          status: "completed",
          description: "تم تقديم الطلب بنجاح",
          notes: ""
        },
        {
          stage: "screening",
          title: "المراجعة الأولية",
          date: "2024-01-22",
          status: "completed",
          description: "تمت مراجعة الطلب والموافقة عليه",
          notes: ""
        },
        {
          stage: "interview_1",
          title: "المقابلة الأولى",
          date: "2024-01-24",
          status: "completed",
          description: "مقابلة مع قسم الموارد البشرية",
          notes: ""
        },
        {
          stage: "offer",
          title: "عرض العمل",
          date: "2024-01-26",
          status: "in_progress",
          description: "تم إرسال عرض العمل",
          notes: "في انتظار رد المرشح"
        },
        {
          stage: "contract",
          title: "توقيع العقد",
          date: null,
          status: "pending",
          description: "توقيع عقد العمل",
          notes: ""
        },
        {
          stage: "guarantee",
          title: "فترة الضمان",
          date: null,
          status: "pending",
          description: "بداية فترة الضمان (3 أشهر)",
          notes: ""
        }
      ]
    }
  ]

  const getStageIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case "in_progress":
        return <Clock className="w-5 h-5 text-blue-600" />
      case "rejected":
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">مكتمل</Badge>
      case "in_progress":
        return <Badge className="bg-blue-100 text-blue-800">جاري</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800">مرفوض</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">في الانتظار</Badge>
    }
  }

  const handleSendNotification = (application: any, type: string) => {
    // محاكاة إرسال الإشعارات
    console.log(`Sending ${type} notification to ${application.candidateName}`)
    alert(`تم إرسال ${type === 'email' ? 'بريد إلكتروني' : type === 'sms' ? 'رسالة نصية' : 'إشعار'} إلى ${application.candidateName}`)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">تايم لاين التوظيف</h1>
            <p className="text-gray-600">تتبع مراحل عملية التوظيف لكل مرشح</p>
          </div>
        </div>

        {/* Applications List */}
        <div className="grid gap-6">
          {applications.map((application) => (
            <Card key={application.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <User className="w-5 h-5" />
                      {application.candidateName}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-4 mt-2">
                      <span className="flex items-center gap-1">
                        <Building className="w-4 h-4" />
                        {application.company}
                      </span>
                      <span>{application.jobTitle}</span>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {application.appliedDate}
                      </span>
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4" />
                          عرض التفاصيل
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle className="text-lg md:text-xl">تايم لاين التوظيف - {application.candidateName}</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 md:space-y-6">
                          {/* Candidate Info */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-4 p-3 md:p-4 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-2 text-xs md:text-sm">
                              <Mail className="w-3 h-3 md:w-4 md:h-4 text-gray-500 flex-shrink-0" />
                              <span className="truncate">{application.candidateEmail}</span>
                            </div>
                            <div className="flex items-center gap-2 text-xs md:text-sm">
                              <Phone className="w-3 h-3 md:w-4 md:h-4 text-gray-500 flex-shrink-0" />
                              <span>{application.candidatePhone}</span>
                            </div>
                            <div className="flex items-center gap-2 text-xs md:text-sm">
                              <Building className="w-3 h-3 md:w-4 md:h-4 text-gray-500 flex-shrink-0" />
                              <span className="truncate">{application.company}</span>
                            </div>
                          </div>

                          {/* Timeline */}
                          <div className="space-y-3 md:space-y-4">
                            <div className="flex items-center justify-between">
                              <h3 className="text-base md:text-lg font-semibold">مراحل التوظيف</h3>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedApplication(application)
                                  setIsEditTimelineOpen(true)
                                }}
                                className="text-xs md:text-sm"
                              >
                                <Edit className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                                تحرير
                              </Button>
                            </div>

                            <div className="relative">
                              {application.timeline.map((stage, index) => (
                                <div key={stage.stage} className="flex items-start gap-2 md:gap-4 pb-4 md:pb-6">
                                  <div className="flex flex-col items-center">
                                    <div className="bg-white rounded-full p-1">
                                      {getStageIcon(stage.status)}
                                    </div>
                                    {index < application.timeline.length - 1 && (
                                      <div className="w-px h-10 md:h-12 bg-gray-300 mt-1 md:mt-2"></div>
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex flex-col md:flex-row md:items-center justify-between mb-1 md:mb-2">
                                      <h4 className="font-medium text-sm md:text-base">{stage.title}</h4>
                                      <div className="flex items-center gap-2 mt-1 md:mt-0">
                                        {getStatusBadge(stage.status)}
                                        {stage.date && (
                                          <span className="text-xs md:text-sm text-gray-500">{stage.date}</span>
                                        )}
                                      </div>
                                    </div>
                                    <p className="text-xs md:text-sm text-gray-600 mb-1 md:mb-2">{stage.description}</p>
                                    {stage.notes && (
                                      <p className="text-xs md:text-sm text-blue-600 bg-blue-50 p-1 md:p-2 rounded">
                                        {stage.notes}
                                      </p>
                                    )}

                                    {/* أزرار التفاعل */}
                                    {stage.status === 'in_progress' && (
                                      <div className="flex gap-2 mt-2">
                                        <Button size="sm" className="text-xs h-7 bg-red-600 hover:bg-red-700">
                                          عرض التفاصيل
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-wrap gap-2 pt-3 md:pt-4 border-t">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSendNotification(application, 'email')}
                              className="text-xs md:text-sm flex-1 md:flex-none"
                            >
                              <Mail className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                              بريد إلكتروني
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSendNotification(application, 'sms')}
                              className="text-xs md:text-sm flex-1 md:flex-none"
                            >
                              <MessageSquare className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                              رسالة نصية
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSendNotification(application, 'notification')}
                              className="text-xs md:text-sm flex-1 md:flex-none"
                            >
                              <Send className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                              إشعار
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-3 md:p-6">
                {/* Mini Timeline */}
                <div className="flex items-center gap-1 md:gap-2 overflow-x-auto pb-2 scrollbar-hide">
                  {application.timeline.map((stage, index) => (
                    <div key={stage.stage} className="flex items-center gap-1 md:gap-2 min-w-0">
                      <div className="flex flex-col items-center min-w-[60px] md:min-w-[80px]">
                        <div className="bg-white rounded-full p-1 shadow-sm">
                          {getStageIcon(stage.status)}
                        </div>
                        <span className="text-[10px] md:text-xs text-gray-500 mt-1 text-center leading-tight">
                          {stage.title}
                        </span>
                      </div>
                      {index < application.timeline.length - 1 && (
                        <div className="w-4 md:w-8 h-px bg-gray-300 flex-shrink-0"></div>
                      )}
                    </div>
                  ))}
                </div>

                {/* معلومات إضافية للهاتف */}
                <div className="mt-3 md:hidden">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>المرحلة الحالية</span>
                    <span className="font-medium">
                      {application.timeline.find(stage => stage.status === 'in_progress')?.title || 'في الانتظار'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Edit Timeline Dialog */}
        <Dialog open={isEditTimelineOpen} onOpenChange={setIsEditTimelineOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>تحرير التايم لاين</DialogTitle>
            </DialogHeader>
            {selectedApplication && (
              <div className="space-y-4">
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {selectedApplication.timeline.map((stage: any, index: number) => (
                    <div key={stage.stage} className="p-4 border rounded-lg">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>عنوان المرحلة</Label>
                          <Input defaultValue={stage.title} />
                        </div>
                        <div className="space-y-2">
                          <Label>الحالة</Label>
                          <Select defaultValue={stage.status}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="pending">في الانتظار</SelectItem>
                              <SelectItem value="in_progress">جاري</SelectItem>
                              <SelectItem value="completed">مكتمل</SelectItem>
                              <SelectItem value="rejected">مرفوض</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div className="space-y-2">
                          <Label>التاريخ</Label>
                          <Input type="date" defaultValue={stage.date} />
                        </div>
                        <div className="space-y-2">
                          <Label>الوصف</Label>
                          <Input defaultValue={stage.description} />
                        </div>
                      </div>
                      <div className="space-y-2 mt-4">
                        <Label>ملاحظات</Label>
                        <Textarea defaultValue={stage.notes} rows={2} />
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex gap-2 pt-4">
                  <Button 
                    className="bg-red-600 hover:bg-red-700 flex-1"
                    onClick={() => setIsEditTimelineOpen(false)}
                  >
                    حفظ التغييرات
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsEditTimelineOpen(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
