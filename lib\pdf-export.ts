"use client"

import jsPD<PERSON> from "jspdf"

interface ReportData {
  title: string
  subtitle?: string
  data: any[]
  charts?: any[]
  summary?: any
}

export class PDFExportService {
  static async exportToPDF(reportData: ReportData): Promise<void> {
    try {
      const doc = new jsPDF()

      // Configure for RTL and Arabic text
      doc.setR2L(true)
      doc.setFont("helvetica")
      doc.setLanguage("ar")

      // Title (using English characters for Arabic text to avoid encoding issues)
      doc.setFontSize(16)
      doc.text("Yas Global Partner - Monthly Report", 105, 20, { align: "center" })

      // Current date
      const date = new Date().toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
      doc.setFontSize(10)
      doc.text(`Report Date: ${date}`, 20, 35)

      // Company info
      doc.text("Yas Global Partner", 20, 45)
      doc.text("HR Management System", 20, 52)

      // Summary if available
      let yPosition = 65
      if (reportData.summary) {
        doc.setFontSize(14)
        doc.text("Report Summary", 20, yPosition)
        yPosition += 10
        doc.setFontSize(10)
        Object.entries(reportData.summary).forEach(([key, value]) => {
          // Convert Arabic keys to English for PDF compatibility
          const englishKey = this.translateToEnglish(key)
          doc.text(`${englishKey}: ${value}`, 20, yPosition)
          yPosition += 7
        })
        yPosition += 10
      }

      // Table header
      doc.setFontSize(12)
      doc.text("Detailed Data", 20, yPosition)
      yPosition += 10

      // Table data
      if (reportData.data.length > 0) {
        const headers = Object.keys(reportData.data[0])
        const columnWidth = 170 / headers.length

        // Draw headers
        doc.setFontSize(10)
        doc.setFont("helvetica", "bold")
        headers.forEach((header, index) => {
          const englishHeader = this.translateToEnglish(header)
          doc.text(englishHeader, 20 + index * columnWidth, yPosition)
        })

        // Draw header line
        doc.line(20, yPosition + 2, 190, yPosition + 2)
        yPosition += 10

        // Draw data rows
        doc.setFont("helvetica", "normal")
        reportData.data.forEach((row, rowIndex) => {
          if (yPosition > 270) {
            doc.addPage()
            yPosition = 20
          }

          headers.forEach((header, colIndex) => {
            let cellValue = String(row[header] || "")
            // Convert Arabic months to English
            cellValue = this.translateToEnglish(cellValue)
            const truncatedValue = cellValue.length > 15 ? cellValue.substring(0, 15) + "..." : cellValue
            doc.text(truncatedValue, 20 + colIndex * columnWidth, yPosition)
          })

          yPosition += 8
        })
      }

      // Footer
      const pageCount = doc.getNumberOfPages()
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(8)
        doc.text(`Page ${i} of ${pageCount}`, 105, 290, { align: "center" })
        doc.text("Generated by Yas Global Partner System", 105, 285, { align: "center" })
      }

      // Save the PDF
      const fileName = `${reportData.title.replace(/\s+/g, "_")}_${new Date().toISOString().split("T")[0]}.pdf`
      doc.save(fileName)
    } catch (error) {
      console.error("Error exporting PDF:", error)
      // Fallback to CSV export
      this.exportToCSV(reportData.data, reportData.title)
    }
  }

  static exportToCSV(data: any[], filename = "تقرير") {
    try {
      if (!data.length) return false

      const headers = Object.keys(data[0])
      const csvContent = [
        headers.join(","),
        ...data.map((row) => headers.map((header) => `"${row[header] || ""}"`).join(",")),
      ].join("\n")

      // Add BOM for proper Arabic display
      const BOM = "\uFEFF"
      const blob = new Blob([BOM + csvContent], { type: "text/csv;charset=utf-8;" })
      const link = document.createElement("a")

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob)
        link.setAttribute("href", url)
        link.setAttribute("download", `${filename}-${Date.now()}.csv`)
        link.style.visibility = "hidden"
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
      }

      return true
    } catch (error) {
      console.error("Error exporting CSV:", error)
      return false
    }
  }

  static async exportToExcel(data: any[], filename = "تقرير") {
    try {
      // Fallback to CSV for now
      return this.exportToCSV(data, filename)
    } catch (error) {
      console.error("Error exporting Excel:", error)
      return false
    }
  }

  private static translateToEnglish(text: string): string {
    const translations: { [key: string]: string } = {
      // Months
      "يناير": "January",
      "فبراير": "February",
      "مارس": "March",
      "أبريل": "April",
      "مايو": "May",
      "يونيو": "June",
      "يوليو": "July",
      "أغسطس": "August",
      "سبتمبر": "September",
      "أكتوبر": "October",
      "نوفمبر": "November",
      "ديسمبر": "December",

      // Report fields
      "إجمالي الوظائف": "Total Jobs",
      "إجمالي المرشحين": "Total Candidates",
      "إجمالي الموظفين": "Total Hired",
      "إجمالي الإيرادات": "Total Revenue",
      "الشهر": "Month",
      "الوظائف": "Jobs",
      "المرشحين": "Candidates",
      "الموظفين": "Hired",
      "الإيرادات": "Revenue",

      // Common terms
      "ريال": "SAR",
      "تقرير الأداء الشهري": "Monthly Performance Report",
      "ملخص التقرير": "Report Summary",
      "البيانات التفصيلية": "Detailed Data"
    }

    return translations[text] || text
  }
}
