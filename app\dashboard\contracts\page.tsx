"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  FileText,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Calendar,
  Building,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"

export default function ContractsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isNewContractDialogOpen, setIsNewContractDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedContract, setSelectedContract] = useState<any>(null)

  const handleViewContract = (contract: any) => {
    setSelectedContract(contract)
    setIsViewDialogOpen(true)
  }

  const handleEditContract = (contract: any) => {
    setSelectedContract(contract)
    setIsEditDialogOpen(true)
  }

  const handleDownloadContract = (contract: any) => {
    const element = document.createElement('a')
    const file = new Blob([`عقد: ${contract.title}\nالعميل: ${contract.client}\nالقيمة: ${contract.value} ${contract.currency}`], {type: 'text/plain'})
    element.href = URL.createObjectURL(file)
    element.download = `contract-${contract.id}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  // بيانات وهمية للعقود
  const contracts = [
    {
      id: 1,
      title: "عقد توظيف أرامكو السعودية",
      client: "أرامكو السعودية",
      type: "recruitment",
      status: "active",
      startDate: "2024-01-15",
      endDate: "2024-12-31",
      value: 500000,
      currency: "SAR",
      positions: 25,
      filled: 18,
      description: "عقد توظيف مهندسين وفنيين للمشاريع البترولية"
    },
    {
      id: 2,
      title: "عقد استشارات البنك الأهلي",
      client: "البنك الأهلي السعودي",
      type: "consulting",
      status: "pending",
      startDate: "2024-03-01",
      endDate: "2024-08-31",
      value: 150000,
      currency: "SAR",
      positions: 0,
      filled: 0,
      description: "استشارات إدارية وتطوير الموارد البشرية"
    },
    {
      id: 3,
      title: "عقد توظيف سابك",
      client: "سابك",
      type: "recruitment",
      status: "completed",
      startDate: "2023-06-01",
      endDate: "2023-12-31",
      value: 750000,
      currency: "SAR",
      positions: 40,
      filled: 40,
      description: "توظيف مختصين في الصناعات البتروكيماوية"
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">في الانتظار</Badge>
      case "completed":
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case "expired":
        return <Badge className="bg-red-100 text-red-800">منتهي الصلاحية</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "recruitment":
        return <Building className="w-4 h-4" />
      case "consulting":
        return <FileText className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  const filteredContracts = contracts.filter(contract =>
    contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.client.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">إدارة العقود</h1>
            <p className="text-gray-600">إدارة عقود الشركة مع العملاء</p>
          </div>
          <Dialog open={isNewContractDialogOpen} onOpenChange={setIsNewContractDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-red-600 hover:bg-red-700">
                <Plus className="w-4 h-4 mr-2" />
                عقد جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>إنشاء عقد جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contract-title">عنوان العقد</Label>
                    <Input id="contract-title" placeholder="أدخل عنوان العقد" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="client">العميل</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر العميل" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="aramco">أرامكو السعودية</SelectItem>
                        <SelectItem value="sabic">سابك</SelectItem>
                        <SelectItem value="alahli">البنك الأهلي</SelectItem>
                        <SelectItem value="stc">الاتصالات السعودية</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contract-type">نوع العقد</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر نوع العقد" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="recruitment">توظيف</SelectItem>
                        <SelectItem value="consulting">استشارات</SelectItem>
                        <SelectItem value="training">تدريب</SelectItem>
                        <SelectItem value="package">باقة شاملة</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contract-value">قيمة العقد</Label>
                    <div className="flex gap-2">
                      <Input id="contract-value" type="number" placeholder="0" className="flex-1" />
                      <Select defaultValue="SAR">
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="SAR">ريال</SelectItem>
                          <SelectItem value="USD">دولار</SelectItem>
                          <SelectItem value="EUR">يورو</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-date">تاريخ البداية</Label>
                    <Input id="start-date" type="date" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-date">تاريخ النهاية</Label>
                    <Input id="end-date" type="date" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">وصف العقد</Label>
                  <Textarea id="description" placeholder="أدخل وصف تفصيلي للعقد" rows={3} />
                </div>

                <div className="flex gap-2 pt-4">
                  <Button 
                    className="bg-red-600 hover:bg-red-700 flex-1"
                    onClick={() => setIsNewContractDialogOpen(false)}
                  >
                    إنشاء العقد
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsNewContractDialogOpen(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي العقود</p>
                  <p className="text-2xl font-bold">24</p>
                </div>
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">العقود النشطة</p>
                  <p className="text-2xl font-bold text-green-600">12</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">القيمة الإجمالية</p>
                  <p className="text-2xl font-bold">2.4M ر.س</p>
                </div>
                <DollarSign className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                  <p className="text-2xl font-bold text-orange-600">5</p>
                </div>
                <Clock className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="البحث في العقود..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            تصفية
          </Button>
        </div>

        {/* Contracts List */}
        <div className="grid gap-6">
          {filteredContracts.map((contract) => (
            <Card key={contract.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      {getTypeIcon(contract.type)}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{contract.title}</h3>
                      <p className="text-gray-600">{contract.client}</p>
                      <p className="text-sm text-gray-500 mt-1">{contract.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(contract.status)}
                    <Button variant="outline" size="sm" onClick={() => handleViewContract(contract)}>
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleEditContract(contract)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDownloadContract(contract)}>
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">تاريخ البداية</p>
                    <p className="font-medium">{contract.startDate}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">تاريخ النهاية</p>
                    <p className="font-medium">{contract.endDate}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">القيمة</p>
                    <p className="font-medium">{contract.value.toLocaleString()} {contract.currency}</p>
                  </div>
                  {contract.type === "recruitment" && (
                    <>
                      <div>
                        <p className="text-gray-500">المناصب المطلوبة</p>
                        <p className="font-medium">{contract.positions}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">تم التوظيف</p>
                        <p className="font-medium text-green-600">{contract.filled}</p>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </DashboardLayout>
  )
}
