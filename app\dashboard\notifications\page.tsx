"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Bell, Mail, MessageSquare, Calendar, Users, CheckCircle, Clock, AlertTriangle, Video } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { VideoCall } from "@/components/video-call"

interface Notification {
  id: string
  type: "email" | "sms" | "system" | "interview" | "application"
  title: string
  message: string
  timestamp: string
  read: boolean
  priority: "high" | "medium" | "low"
  actionRequired?: boolean
  actionType?: "schedule_interview" | "review_application" | "start_video_call" | "send_message"
  actionData?: any
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      type: "interview",
      title: "مقابلة مجدولة غداً",
      message: "مقابلة مع المرشح أحمد محمد لوظيفة مطور React في الساعة 10:00 صباحاً",
      timestamp: "منذ ساعة",
      read: false,
      priority: "high",
      actionRequired: true,
      actionType: "start_video_call",
      actionData: {
        candidateName: "أحمد محمد",
        jobTitle: "مطور React Senior",
        meetingTime: "10:00 صباحاً",
      },
    },
    {
      id: "2",
      type: "application",
      title: "طلب توظيف جديد",
      message: "تم استلام طلب جديد من فاطمة أحمد لوظيفة مدير مشاريع",
      timestamp: "منذ 3 ساعات",
      read: false,
      priority: "medium",
      actionRequired: true,
      actionType: "review_application",
      actionData: {
        candidateName: "فاطمة أحمد",
        jobTitle: "مدير مشاريع",
        applicationId: "APP-001",
      },
    },
    {
      id: "3",
      type: "system",
      title: "انتهاء فترة ضمان",
      message: "ستنتهي فترة الضمان للموظف خالد السعد خلال أسبوع",
      timestamp: "منذ 5 ساعات",
      read: true,
      priority: "medium",
      actionRequired: true,
      actionType: "send_message",
      actionData: {
        employeeName: "خالد السعد",
        guaranteeEndDate: "2024-03-01",
      },
    },
    {
      id: "4",
      type: "email",
      title: "رسالة من العميل",
      message: "شركة التقنية المتقدمة أرسلت استفساراً حول حالة الوظيفة الشاغرة",
      timestamp: "أمس",
      read: true,
      priority: "low",
    },
    {
      id: "5",
      type: "sms",
      title: "تأكيد حضور مقابلة",
      message: "المرشح سارة أحمد أكدت حضورها للمقابلة المجدولة غداً",
      timestamp: "أمس",
      read: true,
      priority: "low",
    },
  ])

  const [isVideoCallOpen, setIsVideoCallOpen] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null)
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false)

  const unreadCount = notifications.filter((n) => !n.read).length
  const highPriorityCount = notifications.filter((n) => n.priority === "high" && !n.read).length
  const actionRequiredCount = notifications.filter((n) => n.actionRequired && !n.read).length

  const markAsRead = (id: string) => {
    setNotifications(notifications.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const markAllAsRead = () => {
    setNotifications(notifications.map((n) => ({ ...n, read: true })))
  }

  const handleAction = (notification: Notification) => {
    setSelectedNotification(notification)

    if (notification.actionType === "start_video_call") {
      setIsVideoCallOpen(true)
    } else {
      setIsActionDialogOpen(true)
    }

    // Mark as read when action is taken
    markAsRead(notification.id)
  }

  const handleScheduleInterview = () => {
    alert("تم جدولة المقابلة بنجاح!")
    setIsActionDialogOpen(false)
  }

  const handleReviewApplication = () => {
    // Navigate to candidate review page
    window.location.href = "/dashboard/candidates"
  }

  const handleSendMessage = () => {
    alert("تم إرسال الرسالة بنجاح!")
    setIsActionDialogOpen(false)
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "email":
        return <Mail className="h-5 w-5 text-blue-600" />
      case "sms":
        return <MessageSquare className="h-5 w-5 text-green-600" />
      case "interview":
        return <Calendar className="h-5 w-5 text-purple-600" />
      case "application":
        return <Users className="h-5 w-5 text-orange-600" />
      case "system":
        return <Bell className="h-5 w-5 text-gray-600" />
      default:
        return <Bell className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getActionButtonText = (actionType?: string) => {
    switch (actionType) {
      case "start_video_call":
        return "بدء المكالمة"
      case "schedule_interview":
        return "جدولة مقابلة"
      case "review_application":
        return "مراجعة الطلب"
      case "send_message":
        return "إرسال رسالة"
      default:
        return "اتخاذ إجراء"
    }
  }

  const getActionIcon = (actionType?: string) => {
    switch (actionType) {
      case "start_video_call":
        return <Video className="h-4 w-4" />
      case "schedule_interview":
        return <Calendar className="h-4 w-4" />
      case "review_application":
        return <Users className="h-4 w-4" />
      case "send_message":
        return <MessageSquare className="h-4 w-4" />
      default:
        return <CheckCircle className="h-4 w-4" />
    }
  }

  const filterNotifications = (filter: string) => {
    switch (filter) {
      case "unread":
        return notifications.filter((n) => !n.read)
      case "high":
        return notifications.filter((n) => n.priority === "high")
      case "action":
        return notifications.filter((n) => n.actionRequired)
      default:
        return notifications
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">الإشعارات</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">تتبع جميع الإشعارات والتنبيهات المهمة</p>
          </div>
          <Button onClick={markAllAsRead} variant="outline">
            <CheckCircle className="h-4 w-4 mr-2" />
            تحديد الكل كمقروء
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">إجمالي الإشعارات</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{notifications.length}</p>
                </div>
                <Bell className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">غير مقروءة</p>
                  <p className="text-2xl font-bold text-blue-600">{unreadCount}</p>
                </div>
                <div className="p-2 bg-blue-100 rounded-full">
                  <Bell className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">أولوية عالية</p>
                  <p className="text-2xl font-bold text-red-600">{highPriorityCount}</p>
                </div>
                <div className="p-2 bg-red-100 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">تتطلب إجراء</p>
                  <p className="text-2xl font-bold text-orange-600">{actionRequiredCount}</p>
                </div>
                <div className="p-2 bg-orange-100 rounded-full">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Notifications List */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة الإشعارات</CardTitle>
            <CardDescription>جميع الإشعارات والتنبيهات الواردة</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="space-y-6">
              <TabsList>
                <TabsTrigger value="all">الكل ({notifications.length})</TabsTrigger>
                <TabsTrigger value="unread">غير مقروءة ({unreadCount})</TabsTrigger>
                <TabsTrigger value="high">أولوية عالية ({highPriorityCount})</TabsTrigger>
                <TabsTrigger value="action">تتطلب إجراء ({actionRequiredCount})</TabsTrigger>
              </TabsList>

              {["all", "unread", "high", "action"].map((tabValue) => (
                <TabsContent key={tabValue} value={tabValue} className="space-y-4">
                  {filterNotifications(tabValue).map((notification) => (
                    <div
                      key={notification.id}
                      className={`border rounded-lg p-4 transition-colors ${
                        !notification.read
                          ? "bg-blue-50 dark:bg-blue-950/20 border-blue-200"
                          : "bg-white dark:bg-gray-800"
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 rtl:space-x-reverse flex-1">
                          <div className="flex-shrink-0 mt-1">{getNotificationIcon(notification.type)}</div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                              <h4
                                className={`text-sm font-medium ${!notification.read ? "text-gray-900 dark:text-white" : "text-gray-700 dark:text-gray-300"}`}
                              >
                                {notification.title}
                              </h4>
                              <Badge className={getPriorityColor(notification.priority)}>
                                {notification.priority === "high"
                                  ? "عالية"
                                  : notification.priority === "medium"
                                    ? "متوسطة"
                                    : "منخفضة"}
                              </Badge>
                              {notification.actionRequired && (
                                <Badge variant="outline" className="text-orange-600 border-orange-600">
                                  يتطلب إجراء
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{notification.message}</p>
                            <p className="text-xs text-gray-500">{notification.timestamp}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          {!notification.read && (
                            <Button variant="ghost" size="sm" onClick={() => markAsRead(notification.id)}>
                              تحديد كمقروء
                            </Button>
                          )}
                          {notification.actionRequired && (
                            <Button
                              size="sm"
                              className="bg-[#C0322D] hover:bg-[#C1352F]"
                              onClick={() => handleAction(notification)}
                            >
                              {getActionIcon(notification.actionType)}
                              <span className="mr-2">{getActionButtonText(notification.actionType)}</span>
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>

        {/* Video Call Component */}
        <VideoCall
          isOpen={isVideoCallOpen}
          onClose={() => setIsVideoCallOpen(false)}
          participants={[
            {
              id: "1",
              name: selectedNotification?.actionData?.candidateName || "المرشح",
              role: "مرشح للوظيفة",
            },
          ]}
          meetingTitle={`مقابلة - ${selectedNotification?.actionData?.jobTitle || "الوظيفة"}`}
        />

        {/* Action Dialog */}
        <Dialog open={isActionDialogOpen} onOpenChange={setIsActionDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>اتخاذ إجراء</DialogTitle>
              <DialogDescription>
                {selectedNotification?.actionType === "schedule_interview" && "جدولة مقابلة جديدة"}
                {selectedNotification?.actionType === "review_application" && "مراجعة طلب التوظيف"}
                {selectedNotification?.actionType === "send_message" && "إرسال رسالة"}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {selectedNotification?.actionType === "schedule_interview" && (
                <div>
                  <p>هل تريد جدولة مقابلة مع {selectedNotification.actionData?.candidateName}؟</p>
                  <div className="flex justify-end space-x-2 rtl:space-x-reverse mt-4">
                    <Button variant="outline" onClick={() => setIsActionDialogOpen(false)}>
                      إلغاء
                    </Button>
                    <Button onClick={handleScheduleInterview} className="bg-[#C0322D] hover:bg-[#C1352F]">
                      جدولة المقابلة
                    </Button>
                  </div>
                </div>
              )}

              {selectedNotification?.actionType === "review_application" && (
                <div>
                  <p>سيتم توجيهك لصفحة مراجعة طلب {selectedNotification.actionData?.candidateName}</p>
                  <div className="flex justify-end space-x-2 rtl:space-x-reverse mt-4">
                    <Button variant="outline" onClick={() => setIsActionDialogOpen(false)}>
                      إلغاء
                    </Button>
                    <Button onClick={handleReviewApplication} className="bg-[#C0322D] hover:bg-[#C1352F]">
                      مراجعة الطلب
                    </Button>
                  </div>
                </div>
              )}

              {selectedNotification?.actionType === "send_message" && (
                <div>
                  <p>إرسال رسالة تذكير بخصوص انتهاء فترة الضمان لـ {selectedNotification.actionData?.employeeName}</p>
                  <div className="flex justify-end space-x-2 rtl:space-x-reverse mt-4">
                    <Button variant="outline" onClick={() => setIsActionDialogOpen(false)}>
                      إلغاء
                    </Button>
                    <Button onClick={handleSendMessage} className="bg-[#C0322D] hover:bg-[#C1352F]">
                      إرسال الرسالة
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
