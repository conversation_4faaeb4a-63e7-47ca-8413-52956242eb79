"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Search,
  Filter,
  MapPin,
  Calendar,
  DollarSign,
  Building,
  Building2,
  Clock,
  Users,
  Briefcase,
  Send,
  Eye,
  BookmarkPlus,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Map,
  Heart
} from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { JobMap } from "@/components/job-map"

interface Job {
  id: string
  title: string
  company: string
  location: string
  type: "full-time" | "part-time" | "contract" | "remote"
  level: "entry" | "mid" | "senior" | "executive"
  salary: {
    min: number
    max: number
    currency: string
  }
  description: string
  requirements: string[]
  skills: string[]
  benefits: string[]
  status: "active" | "paused" | "closed" | "draft"
  postedDate: string
  deadline: string
  applicants: number
  views: number
  lat?: number
  lng?: number
  address?: string
}

const mockJobs: Job[] = [
  {
    id: "1",
    title: "مطور React Senior",
    company: "شركة التقنية المتقدمة",
    location: "الرياض، السعودية",
    type: "full-time",
    level: "senior",
    salary: { min: 15000, max: 25000, currency: "SAR" },
    description:
      "نبحث عن مطور React متمرس للانضمام إلى فريقنا التقني المتميز. ستكون مسؤولاً عن تطوير تطبيقات ويب حديثة باستخدام أحدث التقنيات.",
    requirements: ["خبرة 5+ سنوات في React", "إتقان JavaScript و TypeScript", "خبرة في Next.js"],
    skills: ["React", "JavaScript", "TypeScript", "Next.js"],
    benefits: ["تأمين صحي", "إجازات مدفوعة", "تدريب مستمر", "بيئة عمل مرنة"],
    status: "active",
    postedDate: "2024-01-15",
    deadline: "2024-02-15",
    applicants: 45,
    views: 234,
    lat: 24.7136,
    lng: 46.6753,
    address: "طريق الملك فهد، الرياض",
  },
  {
    id: "2",
    title: "مصمم UI/UX",
    company: "وكالة الإبداع الرقمي",
    location: "جدة، السعودية",
    type: "full-time",
    level: "mid",
    salary: { min: 8000, max: 15000, currency: "SAR" },
    description: "مطلوب مصمم UI/UX مبدع لتصميم تجارب مستخدم استثنائية للتطبيقات والمواقع الإلكترونية.",
    requirements: ["خبرة 3+ سنوات في التصميم", "إتقان Figma و Adobe XD", "فهم مبادئ UX"],
    skills: ["UI/UX Design", "Figma", "Adobe XD", "Prototyping"],
    benefits: ["بيئة عمل إبداعية", "مرونة في العمل", "فرص تطوير"],
    status: "active",
    postedDate: "2024-01-18",
    deadline: "2024-02-18",
    applicants: 28,
    views: 156,
    lat: 21.3891,
    lng: 39.8579,
    address: "شارع التحلية، جدة",
  },
  {
    id: "3",
    title: "مهندس DevOps",
    company: "شركة الحلول السحابية",
    location: "الدمام، السعودية",
    type: "full-time",
    level: "senior",
    salary: { min: 18000, max: 28000, currency: "SAR" },
    description: "نبحث عن مهندس DevOps خبير لإدارة البنية التحتية السحابية وأتمتة عمليات النشر.",
    requirements: ["خبرة 4+ سنوات في DevOps", "إتقان AWS/Azure", "خبرة في Docker و Kubernetes"],
    skills: ["DevOps", "AWS", "Docker", "Kubernetes", "CI/CD"],
    benefits: ["راتب تنافسي", "تأمين صحي شامل", "تدريب على أحدث التقنيات"],
    status: "active",
    postedDate: "2024-01-20",
    deadline: "2024-02-20",
    applicants: 32,
    views: 189,
    lat: 26.4207,
    lng: 50.0888,
    address: "الكورنيش، الدمام",
  },
]

export default function AvailableJobsPage() {
  const { user } = useAuth()

  // التأكد من تسجيل الدخول
  if (!user) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">يرجى تسجيل الدخول</h1>
            <p className="text-gray-600">يجب تسجيل الدخول لعرض الوظائف المتاحة</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // تحديد نوع المستخدم
  const isJobSeeker = user.role === 'job_seeker'
  const isHR = user.role === 'hr'
  const isManager = user.role === 'manager'
  const isAdmin = user.role === 'admin'

  const [jobs, setJobs] = useState<Job[]>(mockJobs)
  const [filteredJobs, setFilteredJobs] = useState<Job[]>(mockJobs)
  const [searchTerm, setSearchTerm] = useState("")
  const [locationFilter, setLocationFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [levelFilter, setLevelFilter] = useState<string>("all")
  const [selectedJob, setSelectedJob] = useState<Job | null>(null)
  const [viewMode, setViewMode] = useState<"cards" | "map">("cards")
  const [savedJobs, setSavedJobs] = useState<string[]>([])
  const [appliedJobs, setAppliedJobs] = useState<string[]>([])
  const [aiRecommendations, setAiRecommendations] = useState<Job[]>([])
  const [isAnalyzingCV, setIsAnalyzingCV] = useState(false)
  const [showAiRecommendations, setShowAiRecommendations] = useState(false)

  // Load saved and applied jobs from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(`yas-global-saved-jobs-${user?.id}`)
    const applied = localStorage.getItem(`yas-global-applied-jobs-${user?.id}`)

    if (saved) setSavedJobs(JSON.parse(saved))
    if (applied) setAppliedJobs(JSON.parse(applied))
  }, [user?.id])

  // Filter jobs based on search and filters
  useEffect(() => {
    let filtered = jobs.filter((job) => job.status === "active")

    if (searchTerm) {
      filtered = filtered.filter(
        (job) =>
          job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.skills.some((skill) => skill.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    }

    if (locationFilter !== "all") {
      filtered = filtered.filter((job) => job.location.includes(locationFilter))
    }

    if (typeFilter !== "all") {
      filtered = filtered.filter((job) => job.type === typeFilter)
    }

    if (levelFilter !== "all") {
      filtered = filtered.filter((job) => job.level === levelFilter)
    }

    setFilteredJobs(filtered)
  }, [jobs, searchTerm, locationFilter, typeFilter, levelFilter])

  // تحليل السيرة الذاتية بالذكاء الاصطناعي
  const analyzeCV = async () => {
    if (!isJobSeeker) return

    setIsAnalyzingCV(true)

    // محاكاة تحليل السيرة الذاتية
    setTimeout(() => {
      // توصيات ذكية بناءً على السيرة الذاتية
      const recommendations = jobs.filter(job => {
        // محاكاة خوارزمية الذكاء الاصطناعي
        const userSkills = ['React', 'JavaScript', 'TypeScript', 'Node.js'] // من السيرة الذاتية
        const matchingSkills = job.skills.filter(skill =>
          userSkills.some(userSkill =>
            skill.toLowerCase().includes(userSkill.toLowerCase())
          )
        )
        return matchingSkills.length >= 2
      }).slice(0, 5)

      setAiRecommendations(recommendations)
      setShowAiRecommendations(true)
      setIsAnalyzingCV(false)
    }, 2000)
  }



  const handleSaveJob = (jobId: string) => {
    if (!isJobSeeker) return // فقط الباحثون عن عمل يمكنهم حفظ الوظائف

    const newSavedJobs = savedJobs.includes(jobId) ? savedJobs.filter((id) => id !== jobId) : [...savedJobs, jobId]
    setSavedJobs(newSavedJobs)
    localStorage.setItem(`yas-global-saved-jobs-${user?.id}`, JSON.stringify(newSavedJobs))
  }

  const handleApplyJob = (jobId: string) => {
    if (!isJobSeeker) return // فقط الباحثون عن عمل يمكنهم التقديم

    if (!appliedJobs.includes(jobId)) {
      const newAppliedJobs = [...appliedJobs, jobId]
      setAppliedJobs(newAppliedJobs)
      localStorage.setItem(`yas-global-applied-jobs-${user?.id}`, JSON.stringify(newAppliedJobs))
      alert("تم تقديم طلبك بنجاح!")
    }
  }

  const getTypeColor = (type: Job["type"]) => {
    switch (type) {
      case "full-time":
        return "bg-blue-100 text-blue-800"
      case "part-time":
        return "bg-purple-100 text-purple-800"
      case "contract":
        return "bg-orange-100 text-orange-800"
      case "remote":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTypeName = (type: Job["type"]) => {
    switch (type) {
      case "full-time":
        return "دوام كامل"
      case "part-time":
        return "دوام جزئي"
      case "contract":
        return "عقد"
      case "remote":
        return "عن بُعد"
      default:
        return type
    }
  }

  const getLevelName = (level: Job["level"]) => {
    switch (level) {
      case "entry":
        return "مبتدئ"
      case "mid":
        return "متوسط"
      case "senior":
        return "كبير"
      case "executive":
        return "تنفيذي"
      default:
        return level
    }
  }



  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Briefcase className="h-6 w-6" />
              الوظائف المتاحة
            </h1>
            <p className="text-gray-600 mt-1">
              {user?.role === "job_seeker"
                ? "اكتشف الوظائف المناسبة لك وقدم طلبك"
                : "إدارة الوظائف المتاحة ومراجعة الطلبات"}
            </p>
          </div>

          <div className="flex gap-2">
            {isJobSeeker && (
              <Button
                onClick={analyzeCV}
                disabled={isAnalyzingCV}
                variant="outline"
                size="sm"
                className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600"
              >
                {isAnalyzingCV ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    جاري التحليل...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    تحليل السيرة الذاتية بالذكاء الاصطناعي
                  </>
                )}
              </Button>
            )}
            <Button
              variant={viewMode === "cards" ? "default" : "outline"}
              onClick={() => setViewMode("cards")}
              size="sm"
            >
              <Briefcase className="h-4 w-4 mr-2" />
              البطاقات
            </Button>
            <Button variant={viewMode === "map" ? "default" : "outline"} onClick={() => setViewMode("map")} size="sm">
              <Map className="h-4 w-4 mr-2" />
              الخريطة
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="lg:col-span-2">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الوظائف والمهارات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="الموقع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المواقع</SelectItem>
                  <SelectItem value="الرياض">الرياض</SelectItem>
                  <SelectItem value="جدة">جدة</SelectItem>
                  <SelectItem value="الدمام">الدمام</SelectItem>
                  <SelectItem value="مكة">مكة</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="نوع العمل" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="full-time">دوام كامل</SelectItem>
                  <SelectItem value="part-time">دوام جزئي</SelectItem>
                  <SelectItem value="contract">عقد</SelectItem>
                  <SelectItem value="remote">عن بُعد</SelectItem>
                </SelectContent>
              </Select>

              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="المستوى" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المستويات</SelectItem>
                  <SelectItem value="entry">مبتدئ</SelectItem>
                  <SelectItem value="mid">متوسط</SelectItem>
                  <SelectItem value="senior">كبير</SelectItem>
                  <SelectItem value="executive">تنفيذي</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* AI Recommendations for Job Seekers */}
        {isJobSeeker && showAiRecommendations && aiRecommendations.length > 0 && (
          <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-700">
                <FileText className="h-5 w-5" />
                توصيات ذكية بناءً على سيرتك الذاتية
              </CardTitle>
              <CardDescription>
                تم تحليل سيرتك الذاتية وإيجاد هذه الوظائف المناسبة لمهاراتك
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {aiRecommendations.map((job) => (
                  <Card key={job.id} className="border-purple-200 hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-sm">{job.title}</h4>
                          <p className="text-xs text-gray-600">{job.company}</p>
                        </div>
                        <div className="flex items-center justify-between">
                          <Badge className="bg-purple-100 text-purple-800 text-xs">
                            مطابقة {Math.floor(Math.random() * 30 + 70)}%
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedJob(job)}
                            className="text-xs"
                          >
                            عرض التفاصيل
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Content */}
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "cards" | "map")}>
          <TabsContent value="cards" className="space-y-4">
            {/* Jobs Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredJobs.map((job) => (
                <Card key={job.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Header */}
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg mb-1">{job.title}</h3>
                          <div className="flex items-center gap-1 text-gray-600 mb-2">
                            <Building2 className="h-4 w-4" />
                            {job.company}
                          </div>
                          <div className="flex items-center gap-1 text-gray-500 text-sm">
                            <MapPin className="h-3 w-3" />
                            {job.location}
                          </div>
                        </div>
                        {isJobSeeker && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSaveJob(job.id)}
                            className={savedJobs.includes(job.id) ? "text-red-600" : "text-gray-400"}
                          >
                            <Heart className={`h-4 w-4 ${savedJobs.includes(job.id) ? "fill-current" : ""}`} />
                          </Button>
                        )}
                      </div>

                      {/* Badges */}
                      <div className="flex flex-wrap gap-2">
                        <Badge className={getTypeColor(job.type)}>{getTypeName(job.type)}</Badge>
                        <Badge variant="outline">{getLevelName(job.level)}</Badge>
                      </div>

                      {/* Salary */}
                      <div className="flex items-center gap-1 text-green-600 font-medium">
                        <DollarSign className="h-4 w-4" />
                        {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()} {job.salary.currency}
                      </div>

                      {/* Description */}
                      <p className="text-gray-600 text-sm line-clamp-3">{job.description}</p>

                      {/* Skills */}
                      <div className="flex flex-wrap gap-1">
                        {job.skills.slice(0, 3).map((skill, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                        {job.skills.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{job.skills.length - 3}
                          </Badge>
                        )}
                      </div>

                      {/* Footer */}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center gap-4">
                          {/* إخفاء عدد المتقدمين عن الباحث عن عمل */}
                          {user?.role !== "job_seeker" && (
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {job.applicants} متقدم
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(job.deadline).toLocaleDateString("ar-SA")}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2 pt-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1 bg-transparent"
                              onClick={() => setSelectedJob(job)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              عرض التفاصيل
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>{job.title}</DialogTitle>
                            </DialogHeader>
                            {selectedJob && (
                              <div className="space-y-6">
                                {/* Job Header */}
                                <div className="flex items-start justify-between">
                                  <div>
                                    <h2 className="text-xl font-bold mb-2">{selectedJob.title}</h2>
                                    <div className="flex items-center gap-2 text-gray-600 mb-2">
                                      <Building2 className="h-4 w-4" />
                                      {selectedJob.company}
                                    </div>
                                    <div className="flex items-center gap-2 text-gray-500">
                                      <MapPin className="h-3 w-3" />
                                      {selectedJob.location}
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-2xl font-bold text-green-600 mb-1">
                                      {selectedJob.salary.min.toLocaleString()} -{" "}
                                      {selectedJob.salary.max.toLocaleString()}
                                    </div>
                                    <div className="text-sm text-gray-500">{selectedJob.salary.currency} شهرياً</div>
                                  </div>
                                </div>

                                {/* Badges */}
                                <div className="flex flex-wrap gap-2">
                                  <Badge className={getTypeColor(selectedJob.type)}>
                                    {getTypeName(selectedJob.type)}
                                  </Badge>
                                  <Badge variant="outline">{getLevelName(selectedJob.level)}</Badge>
                                  {/* إخفاء عدد المتقدمين عن الباحث عن عمل */}
                                  {user?.role !== "job_seeker" && (
                                    <Badge variant="outline">
                                      <Users className="h-3 w-3 mr-1" />
                                      {selectedJob.applicants} متقدم
                                    </Badge>
                                  )}
                                  <Badge variant="outline">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    ينتهي في {new Date(selectedJob.deadline).toLocaleDateString("ar-SA")}
                                  </Badge>
                                </div>

                                {/* Description */}
                                <div>
                                  <h3 className="font-semibold mb-2">وصف الوظيفة</h3>
                                  <p className="text-gray-600 leading-relaxed">{selectedJob.description}</p>
                                </div>

                                {/* Requirements */}
                                <div>
                                  <h3 className="font-semibold mb-2">المتطلبات</h3>
                                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    {selectedJob.requirements.map((req, index) => (
                                      <li key={index}>{req}</li>
                                    ))}
                                  </ul>
                                </div>

                                {/* Skills */}
                                <div>
                                  <h3 className="font-semibold mb-2">المهارات المطلوبة</h3>
                                  <div className="flex flex-wrap gap-2">
                                    {selectedJob.skills.map((skill, index) => (
                                      <Badge key={index} variant="secondary">
                                        {skill}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>

                                {/* Benefits */}
                                <div>
                                  <h3 className="font-semibold mb-2">المزايا</h3>
                                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    {selectedJob.benefits.map((benefit, index) => (
                                      <li key={index}>{benefit}</li>
                                    ))}
                                  </ul>
                                </div>

                                {/* Actions - فقط للباحثين عن عمل */}
                                {isJobSeeker && (
                                  <div className="flex gap-3 pt-4 border-t">
                                    <Button
                                      onClick={() => handleApplyJob(selectedJob.id)}
                                      disabled={appliedJobs.includes(selectedJob.id)}
                                      className="flex-1 bg-[#C0322D] hover:bg-[#C1352F]"
                                    >
                                      <Send className="h-4 w-4 mr-2" />
                                      {appliedJobs.includes(selectedJob.id) ? "تم التقديم" : "تقديم طلب"}
                                    </Button>
                                    <Button
                                      variant="outline"
                                      onClick={() => handleSaveJob(selectedJob.id)}
                                      className={savedJobs.includes(selectedJob.id) ? "text-red-600 border-red-600" : ""}
                                    >
                                      <Heart
                                        className={`h-4 w-4 mr-2 ${savedJobs.includes(selectedJob.id) ? "fill-current" : ""}`}
                                      />
                                      {savedJobs.includes(selectedJob.id) ? "محفوظة" : "حفظ"}
                                    </Button>
                                  </div>
                                )}

                                {/* معلومات إضافية للموارد البشرية والمدراء */}
                                {(isHR || isManager || isAdmin) && (
                                  <div className="flex gap-3 pt-4 border-t">
                                    <Button variant="outline" className="flex-1">
                                      <Users className="h-4 w-4 mr-2" />
                                      عرض المتقدمين ({selectedJob.applicants})
                                    </Button>
                                    <Button variant="outline">
                                      <FileText className="h-4 w-4 mr-2" />
                                      تعديل الوظيفة
                                    </Button>
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>

                        {isJobSeeker && (
                          <Button
                            onClick={() => handleApplyJob(job.id)}
                            disabled={appliedJobs.includes(job.id)}
                            size="sm"
                            className="bg-[#C0322D] hover:bg-[#C1352F]"
                          >
                            <Send className="h-4 w-4 mr-2" />
                            {appliedJobs.includes(job.id) ? "تم التقديم" : "تقديم"}
                          </Button>
                        )}

                        {(isHR || isManager || isAdmin) && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                          >
                            <Users className="h-4 w-4 mr-2" />
                            المتقدمين ({job.applicants})
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredJobs.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">لا توجد وظائف متاحة</h3>
                  <p className="text-gray-600">لم يتم العثور على وظائف مطابقة لمعايير البحث</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="map">
            <JobMap
              jobs={filteredJobs.filter(job => job.lat && job.lng)}
              onJobSelect={setSelectedJob}
              selectedJob={selectedJob}
            />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
