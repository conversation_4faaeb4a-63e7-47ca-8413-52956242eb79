"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

interface Settings {
  siteName: string
  companyName: string
  description: string
  email: string
  phone: string
  address: string
  colorTheme: string
  autoTheme: boolean
  defaultLanguage: string
  timezone: string
  multiLanguage: boolean
  autoDirection: boolean
  aiEnabled: boolean
  aiModel: string
  aiResumeAnalysis: boolean
  aiJobMatching: boolean
  aiRecommendations: boolean
  aiChatbot: boolean
  openaiApiKey: string
  claudeApiKey: string
  geminiApiKey: string
}

interface SettingsContextType {
  settings: Settings
  updateSettings: (updates: Partial<Settings>) => void
  resetSettings: () => void
}

const defaultSettings: Settings = {
  siteName: "Yas Global",
  companyName: "Yas Global Partner",
  description: "شريكك الموثوق في حلول الموارد البشرية والتوظيف",
  email: "<EMAIL>",
  phone: "+966 11 234 5678",
  address: "الرياض، المملكة العربية السعودية",
  colorTheme: "dark-red",
  autoTheme: false,
  defaultLanguage: "ar",
  timezone: "Asia/Riyadh",
  multiLanguage: true,
  autoDirection: true,
  aiEnabled: true,
  aiModel: "gpt-4",
  aiResumeAnalysis: true,
  aiJobMatching: true,
  aiRecommendations: true,
  aiChatbot: false,
  openaiApiKey: "",
  claudeApiKey: "",
  geminiApiKey: "",
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function SettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<Settings>(defaultSettings)

  useEffect(() => {
    // Load settings from localStorage on mount
    const savedSettings = localStorage.getItem("yas-global-settings")
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        const mergedSettings = { ...defaultSettings, ...parsed }
        setSettings(mergedSettings)
        applyThemeSettings(mergedSettings)
      } catch (error) {
        console.error("Error loading settings:", error)
        setSettings(defaultSettings)
      }
    } else {
      applyThemeSettings(defaultSettings)
    }
  }, [])

  const applyThemeSettings = (newSettings: Settings) => {
    const root = document.documentElement

    // Remove existing theme classes
    document.body.classList.remove("theme-dark-red", "theme-blue", "theme-green", "theme-gray", "theme-black")

    // Apply new theme class
    document.body.classList.add(`theme-${newSettings.colorTheme}`)

    // Apply CSS custom properties
    switch (newSettings.colorTheme) {
      case "dark-red":
        root.style.setProperty("--primary", "355 78% 45%")
        root.style.setProperty("--primary-foreground", "210 40% 98%")
        root.style.setProperty("--accent", "355 78% 50%")
        root.style.setProperty("--accent-foreground", "210 40% 98%")
        break
      case "blue":
        root.style.setProperty("--primary", "221 83% 53%")
        root.style.setProperty("--primary-foreground", "210 40% 98%")
        root.style.setProperty("--accent", "221 83% 58%")
        root.style.setProperty("--accent-foreground", "210 40% 98%")
        break
      case "green":
        root.style.setProperty("--primary", "142 76% 36%")
        root.style.setProperty("--primary-foreground", "210 40% 98%")
        root.style.setProperty("--accent", "142 76% 41%")
        root.style.setProperty("--accent-foreground", "210 40% 98%")
        break
      case "gray":
        root.style.setProperty("--primary", "220 9% 46%")
        root.style.setProperty("--primary-foreground", "210 40% 98%")
        root.style.setProperty("--accent", "220 9% 51%")
        root.style.setProperty("--accent-foreground", "210 40% 98%")
        break
      case "black":
        root.style.setProperty("--primary", "0 0% 7%")
        root.style.setProperty("--primary-foreground", "210 40% 98%")
        root.style.setProperty("--accent", "0 0% 12%")
        root.style.setProperty("--accent-foreground", "210 40% 98%")
        break
    }
  }

  const updateSettings = (updates: Partial<Settings>) => {
    const newSettings = { ...settings, ...updates }
    setSettings(newSettings)
    localStorage.setItem("yas-global-settings", JSON.stringify(newSettings))
    applyThemeSettings(newSettings)
  }

  const resetSettings = () => {
    setSettings(defaultSettings)
    localStorage.setItem("yas-global-settings", JSON.stringify(defaultSettings))
    applyThemeSettings(defaultSettings)
  }

  return (
    <SettingsContext.Provider value={{ settings, updateSettings, resetSettings }}>{children}</SettingsContext.Provider>
  )
}

export function useSettings() {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider")
  }
  return context
}
