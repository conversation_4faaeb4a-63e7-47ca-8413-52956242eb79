"use client"

import { useState, useRef, useEffect } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Video,
  VideoOff,
  Mic,
  Mic<PERSON>ff,
  PhoneOff,
  Monitor,
  MonitorOff,
  MessageSquare,
  CameraOff,
  User,
  Send,
  Copy,
  Link,
  Share2,
  Plus,
  Edit,
  Circle,
  Square
} from "lucide-react"

// بيانات المقابلة
const interviewData = {
  id: 1,
  candidateName: "أحمد محمد السالم",
  candidateEmail: "<EMAIL>",
  jobTitle: "مهندس بترول أول",
  company: "شركة أرامكو السعودية",
  interviewDate: "2024-01-25",
  interviewTime: "10:00",
  interviewType: "تقني",
  duration: "60 دقيقة",
  interviewer: "سارة أحمد - مدير الموارد البشرية"
}

export default function VideoInterviewPage() {
  // حالات المقابلة الأساسية
  const [isInCall, setIsInCall] = useState(false)
  const [isCameraOn, setIsCameraOn] = useState(false)
  const [isMicOn, setIsMicOn] = useState(false)
  const [isScreenSharing, setIsScreenSharing] = useState(false)
  const [callDuration, setCallDuration] = useState(0)
  const [connectionQuality, setConnectionQuality] = useState("excellent")
  const [isRecording, setIsRecording] = useState(false)
  const [recordedChunks, setRecordedChunks] = useState<Blob[]>([])
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)

  // حالات الشات والمشاركين
  const [chatMessages, setChatMessages] = useState<any[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [participants, setParticipants] = useState<any[]>([
    { id: 1, name: "أحمد محمد السالم", role: "مرشح", isOnline: true, isMuted: false, isCameraOn: true },
    { id: 2, name: "سارة أحمد", role: "مدير الموارد البشرية", isOnline: true, isMuted: true, isCameraOn: true }
  ])
  const [meetingLink, setMeetingLink] = useState("")

  // المراجع
  const videoRef = useRef<HTMLVideoElement>(null)
  const screenVideoRef = useRef<HTMLVideoElement>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // محاكاة المستخدم
  const user = {
    id: 1,
    name: "سارة أحمد",
    role: "hr", // hr, admin, job_seeker
    permissions: ["manage_interviews", "view_analytics"]
  }

  // تنسيق الوقت
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // بدء المقابلة
  const startInterview = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          frameRate: { ideal: 30 }
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play().catch(console.error)
      }

      setIsInCall(true)
      setIsCameraOn(true)
      setIsMicOn(true)

      // توليد رابط الاجتماع
      if (!meetingLink) {
        generateMeetingLink()
      }

      // بدء عداد الوقت
      timerRef.current = setInterval(() => {
        setCallDuration(prev => prev + 1)
      }, 1000)

    } catch (error) {
      console.error('خطأ في الوصول للكاميرا:', error)
      alert('لا يمكن الوصول للكاميرا والميكروفون. تأكد من منح الأذونات المطلوبة.')
    }
  }

  // إنهاء المقابلة
  const endInterview = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }
    
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      videoRef.current.srcObject = null
    }

    if (screenVideoRef.current?.srcObject) {
      const stream = screenVideoRef.current.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      screenVideoRef.current.srcObject = null
    }

    setIsInCall(false)
    setIsCameraOn(false)
    setIsMicOn(false)
    setIsScreenSharing(false)
    setCallDuration(0)
  }

  // تبديل الكاميرا
  const toggleCamera = async () => {
    try {
      if (!isCameraOn) {
        // طلب أذونات الكاميرا والميكروفون
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 },
            frameRate: { ideal: 30, max: 60 },
            facingMode: 'user'
          },
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        })

        if (videoRef.current) {
          videoRef.current.srcObject = stream
          // التأكد من تشغيل الفيديو
          try {
            await videoRef.current.play()
            console.log('Camera started successfully')
          } catch (playError) {
            console.error('Play error:', playError)
            // محاولة تشغيل الفيديو مرة أخرى
            setTimeout(() => {
              videoRef.current?.play().catch(console.error)
            }, 100)
          }
        }
        setIsCameraOn(true)
        setIsMicOn(true)
      } else {
        if (videoRef.current?.srcObject) {
          const stream = videoRef.current.srcObject as MediaStream
          const videoTrack = stream.getVideoTracks()[0]
          if (videoTrack) {
            videoTrack.enabled = false
          }
        }
        setIsCameraOn(false)
      }
    } catch (error) {
      console.error('خطأ في الوصول للكاميرا:', error)

      // رسائل خطأ مفصلة
      let errorMessage = 'لا يمكن الوصول للكاميرا. '
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage += 'يرجى السماح بالوصول للكاميرا والميكروفون من إعدادات المتصفح.'
        } else if (error.name === 'NotFoundError') {
          errorMessage += 'لم يتم العثور على كاميرا متصلة.'
        } else if (error.name === 'NotReadableError') {
          errorMessage += 'الكاميرا مستخدمة من تطبيق آخر.'
        } else {
          errorMessage += 'خطأ غير معروف: ' + error.message
        }
      }

      alert(errorMessage)
    }
  }

  // بدء/إيقاف التسجيل
  const toggleRecording = async () => {
    try {
      if (!isRecording) {
        // بدء التسجيل
        if (videoRef.current?.srcObject) {
          const stream = videoRef.current.srcObject as MediaStream

          // إنشاء MediaRecorder
          const mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'video/webm;codecs=vp9,opus'
          })

          mediaRecorderRef.current = mediaRecorder
          const chunks: Blob[] = []

          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              chunks.push(event.data)
            }
          }

          mediaRecorder.onstop = () => {
            const blob = new Blob(chunks, { type: 'video/webm' })
            setRecordedChunks([blob])

            // إنشاء رابط تحميل
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `interview-recording-${new Date().toISOString().slice(0, 19)}.webm`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)

            console.log('Recording saved successfully')
          }

          mediaRecorder.start(1000) // حفظ كل ثانية
          setIsRecording(true)
          console.log('Recording started')
        } else {
          alert('يجب تشغيل الكاميرا أولاً لبدء التسجيل')
        }
      } else {
        // إيقاف التسجيل
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.stop()
          setIsRecording(false)
          console.log('Recording stopped')
        }
      }
    } catch (error) {
      console.error('خطأ في التسجيل:', error)
      alert('حدث خطأ أثناء التسجيل. تأكد من دعم المتصفح لهذه الميزة.')
    }
  }

  // تبديل الميكروفون
  const toggleMic = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      const audioTrack = stream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !isMicOn
      }
    }
    setIsMicOn(!isMicOn)
  }

  // مشاركة الشاشة
  const toggleScreenShare = async () => {
    try {
      if (!isScreenSharing) {
        // طلب مشاركة الشاشة
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1920, max: 3840 },
            height: { ideal: 1080, max: 2160 },
            frameRate: { ideal: 30, max: 60 }
          },
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        })

        if (screenVideoRef.current) {
          screenVideoRef.current.srcObject = stream
          try {
            await screenVideoRef.current.play()
            console.log('Screen sharing started successfully')
          } catch (playError) {
            console.error('Screen share play error:', playError)
            setTimeout(() => {
              screenVideoRef.current?.play().catch(console.error)
            }, 100)
          }
        }

        setIsScreenSharing(true)

        // إيقاف المشاركة عند إغلاق النافذة أو إيقاف المشاركة
        stream.getVideoTracks()[0].onended = () => {
          console.log('Screen sharing ended by user')
          setIsScreenSharing(false)
          if (screenVideoRef.current) {
            screenVideoRef.current.srcObject = null
          }
        }
      } else {
        // إيقاف مشاركة الشاشة
        if (screenVideoRef.current?.srcObject) {
          const stream = screenVideoRef.current.srcObject as MediaStream
          stream.getTracks().forEach(track => {
            track.stop()
            console.log('Screen share track stopped:', track.kind)
          })
          screenVideoRef.current.srcObject = null
        }
        setIsScreenSharing(false)
        console.log('Screen sharing stopped manually')
      }
    } catch (error) {
      console.error('خطأ في مشاركة الشاشة:', error)

      let errorMessage = 'لا يمكن مشاركة الشاشة. '
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage += 'يرجى السماح بمشاركة الشاشة.'
        } else if (error.name === 'NotSupportedError') {
          errorMessage += 'مشاركة الشاشة غير مدعومة في هذا المتصفح.'
        } else {
          errorMessage += 'خطأ: ' + error.message
        }
      }

      alert(errorMessage)
    }
  }

  // توليد رابط الاجتماع
  const generateMeetingLink = () => {
    const linkId = Math.random().toString(36).substring(2, 15)
    const link = `https://yasglobal.com/meet/${linkId}`
    setMeetingLink(link)
  }

  // نسخ رابط الاجتماع
  const copyMeetingLink = () => {
    navigator.clipboard.writeText(meetingLink)
    alert('تم نسخ الرابط!')
  }

  // إرسال رسالة في الشات
  const sendMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: Date.now(),
        sender: user.name,
        text: newMessage,
        time: new Date().toLocaleTimeString('ar-SA', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        timestamp: new Date().toISOString()
      }
      setChatMessages(prev => [...prev, message])
      setNewMessage("")
    }
  }

  // محاكاة جودة الاتصال
  useEffect(() => {
    if (isInCall) {
      const interval = setInterval(() => {
        const qualities = ["excellent", "good", "fair", "poor"]
        const randomQuality = qualities[Math.floor(Math.random() * qualities.length)]
        setConnectionQuality(randomQuality)
      }, 10000)
      
      return () => clearInterval(interval)
    }
  }, [isInCall])

  // تنظيف الموارد عند إغلاق المكون
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (videoRef.current?.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream
        stream.getTracks().forEach(track => track.stop())
      }
      if (screenVideoRef.current?.srcObject) {
        const stream = screenVideoRef.current.srcObject as MediaStream
        stream.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* العنوان */}
        <div>
          <h1 className="text-3xl font-bold">المقابلات الأونلاين</h1>
          <p className="text-gray-600 mt-1">نظام مقابلات متقدم مع تسجيل وتحليل ذكي</p>
        </div>

        {/* معلومات المقابلة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              معلومات المقابلة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-500">المرشح</Label>
                <p className="font-semibold">{interviewData.candidateName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">الوظيفة</Label>
                <p className="font-semibold">{interviewData.jobTitle}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">الشركة</Label>
                <p className="font-semibold">{interviewData.company}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">التاريخ والوقت</Label>
                <div className="flex items-center gap-2">
                  <p className="font-semibold">{interviewData.interviewDate} - {interviewData.interviewTime}</p>
                  {(user?.role === 'admin' || user?.role === 'hr') && (
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="p-1 h-6 w-6"
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* أزرار التحكم الرئيسية */}
        <div className="flex gap-4">
          {!isInCall ? (
            <Button onClick={startInterview} size="lg" className="bg-green-600 hover:bg-green-700">
              <Video className="w-5 h-5 mr-2" />
              بدء المقابلة
            </Button>
          ) : (
            <Button onClick={endInterview} size="lg" variant="destructive">
              <PhoneOff className="w-5 h-5 mr-2" />
              إنهاء المقابلة
            </Button>
          )}

          {isInCall && (
            <>
              <Button onClick={toggleCamera} variant={isCameraOn ? "default" : "destructive"}>
                {isCameraOn ? <Video className="w-4 h-4 mr-2" /> : <VideoOff className="w-4 h-4 mr-2" />}
                {isCameraOn ? "إيقاف الكاميرا" : "تشغيل الكاميرا"}
              </Button>

              <Button onClick={toggleMic} variant={isMicOn ? "default" : "destructive"}>
                {isMicOn ? <Mic className="w-4 h-4 mr-2" /> : <MicOff className="w-4 h-4 mr-2" />}
                {isMicOn ? "إيقاف الميكروفون" : "تشغيل الميكروفون"}
              </Button>

              <Button onClick={toggleScreenShare} variant={isScreenSharing ? "secondary" : "outline"}>
                {isScreenSharing ? <Monitor className="w-4 h-4 mr-2" /> : <Monitor className="w-4 h-4 mr-2" />}
                {isScreenSharing ? "إيقاف مشاركة الشاشة" : "مشاركة الشاشة"}
              </Button>

              <Button
                onClick={toggleRecording}
                variant={isRecording ? "destructive" : "outline"}
                className={isRecording ? "animate-pulse" : ""}
              >
                {isRecording ? <Square className="w-4 h-4 mr-2" /> : <Circle className="w-4 h-4 mr-2" />}
                {isRecording ? "إيقاف التسجيل" : "بدء التسجيل"}
              </Button>
            </>
          )}
        </div>

        {/* واجهة الفيديو */}
        {isInCall && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* الفيديو الرئيسي */}
            <div className="lg:col-span-3">
              <Card>
                <CardContent className="p-0">
                  <div className="relative bg-black rounded-lg overflow-hidden min-h-[400px]" style={{ aspectRatio: '16/9' }}>
                    {/* مؤشر التسجيل */}
                    {isRecording && (
                      <div className="absolute top-4 right-4 z-10 flex items-center gap-2 bg-red-600 text-white px-3 py-1 rounded-full animate-pulse">
                        <Circle className="w-3 h-3 fill-current" />
                        <span className="text-sm font-medium">جاري التسجيل</span>
                        <span className="text-xs">
                          {Math.floor(callDuration / 60).toString().padStart(2, '0')}:
                          {(callDuration % 60).toString().padStart(2, '0')}
                        </span>
                      </div>
                    )}
                    {/* فيديو الكاميرا */}
                    {isCameraOn && (
                      <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        muted
                        className="w-full h-full object-cover"
                        style={{ minHeight: '400px' }}
                        onLoadedMetadata={() => {
                          console.log('Video loaded successfully')
                        }}
                        onError={(e) => {
                          console.error('Video error:', e)
                        }}
                      />
                    )}

                    {/* فيديو مشاركة الشاشة */}
                    {isScreenSharing && (
                      <video
                        ref={screenVideoRef}
                        autoPlay
                        playsInline
                        muted
                        className="w-full h-full object-contain bg-gray-900"
                        style={{ minHeight: '400px' }}
                        onLoadedMetadata={() => {
                          console.log('Screen share loaded successfully')
                        }}
                        onError={(e) => {
                          console.error('Screen share error:', e)
                        }}
                      />
                    )}

                    {/* رسالة عدم وجود كاميرا */}
                    {!isCameraOn && !isScreenSharing && (
                      <div className="w-full h-full flex items-center justify-center text-white min-h-[400px]">
                        <div className="text-center">
                          <CameraOff className="w-16 h-16 mx-auto mb-4 opacity-50" />
                          <p className="text-lg">الكاميرا مغلقة</p>
                          <p className="text-sm opacity-75 mt-2">اضغط على زر الكاميرا لبدء الفيديو</p>
                        </div>
                      </div>
                    )}

                    {/* أدوات التحكم */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 bg-black/50 p-2 rounded-lg">
                      <Button size="sm" variant={isCameraOn ? "default" : "destructive"} onClick={toggleCamera}>
                        {isCameraOn ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                      </Button>
                      <Button size="sm" variant={isMicOn ? "default" : "destructive"} onClick={toggleMic}>
                        {isMicOn ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                      </Button>
                      <Button size="sm" variant={isScreenSharing ? "secondary" : "outline"} onClick={toggleScreenShare}>
                        {isScreenSharing ? <Monitor className="w-4 h-4" /> : <Monitor className="w-4 h-4" />}
                      </Button>
                      <Button
                        size="sm"
                        variant={isRecording ? "destructive" : "outline"}
                        onClick={toggleRecording}
                        className={isRecording ? "animate-pulse" : ""}
                      >
                        {isRecording ? <Square className="w-4 h-4" /> : <Circle className="w-4 h-4" />}
                      </Button>
                      <Button size="sm" variant="destructive" onClick={endInterview}>
                        <PhoneOff className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* مؤشر الوقت */}
                    <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                      {formatTime(callDuration)}
                    </div>

                    {/* مؤشر جودة الاتصال */}
                    <div className="absolute top-4 left-4">
                      <div className={`px-2 py-1 rounded text-xs text-white ${
                        connectionQuality === 'excellent' ? 'bg-green-600' :
                        connectionQuality === 'good' ? 'bg-blue-600' :
                        connectionQuality === 'fair' ? 'bg-yellow-600' : 'bg-red-600'
                      }`}>
                        {connectionQuality === 'excellent' ? 'ممتاز' :
                         connectionQuality === 'good' ? 'جيد' :
                         connectionQuality === 'fair' ? 'متوسط' : 'ضعيف'}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* الشات والأدوات الجانبية */}
            <div className="space-y-4">
              {/* الشات */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <MessageSquare className="w-4 h-4" />
                    الشات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="h-40 overflow-y-auto space-y-2 border rounded p-2">
                    {chatMessages.length === 0 ? (
                      <p className="text-gray-500 text-sm text-center">لا توجد رسائل</p>
                    ) : (
                      chatMessages.map((message) => (
                        <div key={message.id} className="text-sm">
                          <div className="font-medium text-blue-600">{message.sender}</div>
                          <div className="text-gray-800">{message.text}</div>
                          <div className="text-xs text-gray-400">{message.time}</div>
                        </div>
                      ))
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="اكتب رسالة..."
                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      className="text-sm"
                    />
                    <Button size="sm" onClick={sendMessage}>
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* المشاركون */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">المشاركون المتصلون</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {participants.filter(p => p.isOnline).map((participant) => (
                      <div key={participant.id} className="flex items-center gap-2 text-sm">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">{participant.name}</div>
                          <div className="text-xs text-gray-500">{participant.role}</div>
                        </div>
                        <div className="flex gap-1">
                          {!participant.isMuted && <Mic className="w-3 h-3 text-green-600" />}
                          {participant.isCameraOn && <Video className="w-3 h-3 text-blue-600" />}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* رابط الاجتماع */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Link className="w-5 h-5" />
              رابط الاجتماع
            </CardTitle>
          </CardHeader>
          <CardContent>
            {meetingLink ? (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded text-sm break-all">
                  {meetingLink}
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={copyMeetingLink}>
                    <Copy className="w-3 h-3 mr-1" />
                    نسخ الرابط
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => window.open(meetingLink, '_blank')}>
                    <Share2 className="w-3 h-3 mr-1" />
                    فتح في نافذة جديدة
                  </Button>
                </div>
              </div>
            ) : (
              <Button onClick={generateMeetingLink}>
                <Plus className="w-4 h-4 mr-2" />
                إنشاء رابط اجتماع
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
