"use client"

import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Users, Briefcase, TrendingUp } from "lucide-react"
import Link from "next/link"
import { motion } from "framer-motion"

export function HeroSection() {
  return (
    <section className="relative bg-[#C0322D] text-white py-20 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-[#C0322D] to-[#C1352F] opacity-90"></div>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-right"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Transforming HR with
              <span className="block text-[#F28C86]">Excellence</span>
            </h1>
            <p className="text-xl mb-8 text-gray-100 leading-relaxed">
              نحن نقدم حلول شاملة للموارد البشرية والتوظيف، مع خبرة تزيد عن 10 سنوات في تطوير المواهب وبناء الفرق
              المتميزة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link href="/jobs">
                <Button size="lg" className="bg-white text-[#C0322D] hover:bg-gray-100">
                  استعرض الوظائف
                  <ArrowRight className="mr-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-[#C0322D] bg-transparent"
                >
                  تحدث معنا
                </Button>
              </Link>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Users className="h-12 w-12 mx-auto mb-4 text-[#F28C86]" />
              <h3 className="text-2xl font-bold mb-2">500+</h3>
              <p className="text-gray-200">عميل راضي</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Briefcase className="h-12 w-12 mx-auto mb-4 text-[#F28C86]" />
              <h3 className="text-2xl font-bold mb-2">2000+</h3>
              <p className="text-gray-200">وظيفة تم شغلها</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center sm:col-span-2">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 text-[#F28C86]" />
              <h3 className="text-2xl font-bold mb-2">95%</h3>
              <p className="text-gray-200">معدل نجاح التوظيف</p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
