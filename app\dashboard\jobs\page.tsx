"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Briefcase, Plus, Search, Edit, Trash2, Eye, MapPin, Calendar, DollarSign, Download } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { useAuth } from "@/hooks/use-auth"
import { SkillsSelector } from "@/components/skills-selector"
import { BenefitsSelector } from "@/components/benefits-selector"

interface Job {
  id: string
  title: string
  company: string
  location: string
  locationUrl?: string
  type: "full-time" | "part-time" | "contract" | "remote"
  level: "entry" | "mid" | "senior" | "executive"
  experienceYears: { min: number; max: number }
  salary: {
    min: number
    max: number
    currency: string
  }
  description: string
  requirements: string[]
  skills: string[]
  benefits: string[]
  status: "active" | "paused" | "closed" | "draft"
  postedDate: string
  deadline: string
  applicants: number
  views: number
  createdBy: string
}

const mockJobs: Job[] = [
  {
    id: "1",
    title: "مطور Full Stack Senior",
    company: "شركة أرامكو السعودية",
    location: "الظهران، السعودية",
    locationUrl: "https://maps.google.com/?q=Dhahran,Saudi+Arabia",
    type: "full-time",
    level: "senior",
    experienceYears: { min: 5, max: 8 },
    salary: { min: 18000, max: 28000, currency: "SAR" },
    description:
      "نبحث عن مطور Full Stack متمرس للانضمام إلى فريق التطوير التقني في أرامكو السعودية. المرشح المثالي يجب أن يكون لديه خبرة واسعة في تطوير التطبيقات الحديثة.",
    requirements: [
      "خبرة 5+ سنوات في تطوير الويب",
      "إتقان React و Node.js",
      "خبرة في قواعد البيانات",
      "إجادة اللغة الإنجليزية",
    ],
    skills: ["React", "Node.js", "TypeScript", "MongoDB", "AWS"],
    benefits: ["تأمين صحي شامل", "بدل سكن", "مكافآت أداء", "تدريب وتطوير مهني", "إجازة سنوية مدفوعة"],
    status: "active",
    postedDate: "2024-01-15",
    deadline: "2024-02-15",
    applicants: 67,
    views: 342,
    createdBy: "<EMAIL>",
  },
  {
    id: "2",
    title: "مصمم UI/UX",
    company: "البنك الأهلي السعودي",
    location: "الرياض، السعودية",
    locationUrl: "https://maps.google.com/?q=Riyadh,Saudi+Arabia",
    type: "full-time",
    level: "mid",
    experienceYears: { min: 3, max: 5 },
    salary: { min: 12000, max: 18000, currency: "SAR" },
    description: "مطلوب مصمم UI/UX مبدع لتصميم تجارب مستخدم استثنائية للتطبيقات المصرفية الرقمية.",
    requirements: ["خبرة 3+ سنوات في تصميم UI/UX", "إتقان Figma و Adobe Creative Suite", "فهم مبادئ التصميم المتجاوب"],
    skills: ["Figma", "Adobe XD", "Sketch", "Prototyping", "User Research"],
    benefits: ["تأمين صحي", "مرونة في العمل", "بدل مواصلات", "فرص التطوير المهني"],
    status: "active",
    postedDate: "2024-01-18",
    deadline: "2024-02-18",
    applicants: 43,
    views: 198,
    createdBy: "<EMAIL>",
  },
]

export default function JobsPage() {
  const { user, hasPermission } = useAuth()
  const [jobs, setJobs] = useState<Job[]>(mockJobs)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingJob, setEditingJob] = useState<Job | null>(null)
  const [viewingJob, setViewingJob] = useState<Job | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  const [newJob, setNewJob] = useState({
    title: "",
    company: "",
    location: "",
    locationUrl: "",
    type: "full-time" as Job["type"],
    level: "mid" as Job["level"],
    experienceYears: { min: 0, max: 0 },
    salary: { min: 0, max: 0, currency: "SAR" },
    description: "",
    requirements: [] as string[],
    skills: [] as string[],
    benefits: [] as string[],
    deadline: "",
  })

  // Load jobs from localStorage
  useEffect(() => {
    const savedJobs = localStorage.getItem("yas-global-jobs")
    if (savedJobs) {
      try {
        setJobs(JSON.parse(savedJobs))
      } catch (error) {
        console.error("Error loading jobs:", error)
      }
    }
  }, [])

  // Save jobs to localStorage
  const saveJobs = (newJobs: Job[]) => {
    setJobs(newJobs)
    localStorage.setItem("yas-global-jobs", JSON.stringify(newJobs))
  }

  // Check permissions
  if (!hasPermission("jobs")) {
    return (
      <DashboardLayout>
        <div className="p-6 text-center">
          <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">غير مصرح</h2>
          <p className="text-gray-600">ليس لديك صلاحية لإدارة الوظائف</p>
        </div>
      </DashboardLayout>
    )
  }

  const filteredJobs = jobs.filter((job) => {
    const matchesSearch =
      job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || job.status === statusFilter
    const matchesType = typeFilter === "all" || job.type === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  const handleAddJob = () => {
    const job: Job = {
      id: Date.now().toString(),
      ...newJob,
      status: "draft",
      postedDate: new Date().toISOString().split("T")[0],
      applicants: 0,
      views: 0,
      createdBy: user?.email || "",
    }

    saveJobs([...jobs, job])
    resetForm()
    setIsAddDialogOpen(false)
  }

  const handleEditJob = (job: Job) => {
    setEditingJob(job)
    setNewJob({
      title: job.title,
      company: job.company,
      location: job.location,
      locationUrl: job.locationUrl || "",
      type: job.type,
      level: job.level,
      experienceYears: job.experienceYears,
      salary: job.salary,
      description: job.description,
      requirements: job.requirements,
      skills: job.skills,
      benefits: job.benefits,
      deadline: job.deadline,
    })
  }

  const handleUpdateJob = () => {
    if (!editingJob) return

    const updatedJobs = jobs.map((job) => (job.id === editingJob.id ? { ...job, ...newJob } : job))

    saveJobs(updatedJobs)
    setEditingJob(null)
    resetForm()
  }

  const handleDeleteJob = (jobId: string) => {
    if (confirm("هل أنت متأكد من حذف هذه الوظيفة؟")) {
      saveJobs(jobs.filter((job) => job.id !== jobId))
    }
  }

  const handleStatusChange = (jobId: string, newStatus: Job["status"]) => {
    const updatedJobs = jobs.map((job) => (job.id === jobId ? { ...job, status: newStatus } : job))
    saveJobs(updatedJobs)
  }

  const handleViewJob = (job: Job) => {
    setViewingJob(job)
    setIsViewDialogOpen(true)
  }

  const resetForm = () => {
    setNewJob({
      title: "",
      company: "",
      location: "",
      locationUrl: "",
      type: "full-time",
      level: "mid",
      experienceYears: { min: 0, max: 0 },
      salary: { min: 0, max: 0, currency: "SAR" },
      description: "",
      requirements: [],
      skills: [],
      benefits: [],
      deadline: "",
    })
  }

  const exportToExcel = () => {
    const exportData = filteredJobs.map((job) => ({
      العنوان: job.title,
      الشركة: job.company,
      الموقع: job.location,
      "رابط الموقع": job.locationUrl || "",
      النوع: getTypeName(job.type),
      المستوى: getLevelName(job.level),
      "سنوات الخبرة": `${job.experienceYears.min}-${job.experienceYears.max}`,
      "الراتب الأدنى": job.salary.min,
      "الراتب الأعلى": job.salary.max,
      العملة: job.salary.currency,
      الحالة: getStatusName(job.status),
      "تاريخ النشر": job.postedDate,
      "آخر موعد": job.deadline,
      المتقدمين: job.applicants,
      المشاهدات: job.views,
      "منشئ الوظيفة": job.createdBy,
    }))

    // Create CSV content
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(","),
      ...exportData.map((row) => headers.map((header) => `"${row[header as keyof typeof row] || ""}"`).join(",")),
    ].join("\n")

    // Add BOM for proper Arabic display in Excel
    const BOM = "\uFEFF"
    const blob = new Blob([BOM + csvContent], { type: "text/csv;charset=utf-8;" })

    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `تقرير_الوظائف_${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const getStatusColor = (status: Job["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "paused":
        return "bg-yellow-100 text-yellow-800"
      case "closed":
        return "bg-red-100 text-red-800"
      case "draft":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusName = (status: Job["status"]) => {
    switch (status) {
      case "active":
        return "نشطة"
      case "paused":
        return "متوقفة"
      case "closed":
        return "مغلقة"
      case "draft":
        return "مسودة"
      default:
        return status
    }
  }

  const getTypeColor = (type: Job["type"]) => {
    switch (type) {
      case "full-time":
        return "bg-blue-100 text-blue-800"
      case "part-time":
        return "bg-purple-100 text-purple-800"
      case "contract":
        return "bg-orange-100 text-orange-800"
      case "remote":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTypeName = (type: Job["type"]) => {
    switch (type) {
      case "full-time":
        return "دوام كامل"
      case "part-time":
        return "دوام جزئي"
      case "contract":
        return "عقد"
      case "remote":
        return "عن بُعد"
      default:
        return type
    }
  }

  const getLevelName = (level: Job["level"]) => {
    switch (level) {
      case "entry":
        return "مبتدئ"
      case "mid":
        return "متوسط"
      case "senior":
        return "كبير"
      case "executive":
        return "تنفيذي"
      default:
        return level
    }
  }

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-xl md:text-2xl font-bold flex items-center gap-2">
              <Briefcase className="h-5 w-5 md:h-6 md:w-6" />
              إدارة الوظائف
            </h1>
            <p className="text-gray-600 mt-1 text-sm md:text-base">إدارة الوظائف المتاحة والمنشورة</p>
          </div>

          <div className="flex gap-2 w-full sm:w-auto">
            <Button variant="outline" onClick={exportToExcel} className="bg-transparent flex-1 sm:flex-none">
              <Download className="h-4 w-4 ml-2" />
              <span className="hidden sm:inline">تصدير Excel</span>
            </Button>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#C0322D] hover:bg-[#C1352F] flex-1 sm:flex-none">
                  <Plus className="h-4 w-4 ml-2" />
                  <span className="hidden sm:inline">إضافة وظيفة جديدة</span>
                  <span className="sm:hidden">إضافة</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>إضافة وظيفة جديدة</DialogTitle>
                  <DialogDescription>أدخل تفاصيل الوظيفة الجديدة</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="title">عنوان الوظيفة</Label>
                      <Input
                        id="title"
                        value={newJob.title}
                        onChange={(e) => setNewJob({ ...newJob, title: e.target.value })}
                        placeholder="أدخل عنوان الوظيفة"
                      />
                    </div>
                    <div>
                      <Label htmlFor="company">الشركة</Label>
                      <Input
                        id="company"
                        value={newJob.company}
                        onChange={(e) => setNewJob({ ...newJob, company: e.target.value })}
                        placeholder="أدخل اسم الشركة"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="location">الموقع</Label>
                      <Input
                        id="location"
                        value={newJob.location}
                        onChange={(e) => setNewJob({ ...newJob, location: e.target.value })}
                        placeholder="أدخل موقع الوظيفة"
                      />
                    </div>
                    <div>
                      <Label htmlFor="locationUrl">رابط الموقع (Google Maps)</Label>
                      <Input
                        id="locationUrl"
                        value={newJob.locationUrl}
                        onChange={(e) => setNewJob({ ...newJob, locationUrl: e.target.value })}
                        placeholder="https://maps.google.com/?q=..."
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="deadline">آخر موعد للتقديم</Label>
                      <Input
                        id="deadline"
                        type="date"
                        value={newJob.deadline}
                        onChange={(e) => setNewJob({ ...newJob, deadline: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">نوع الوظيفة</Label>
                      <Select
                        value={newJob.type}
                        onValueChange={(value: Job["type"]) => setNewJob({ ...newJob, type: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="full-time">دوام كامل</SelectItem>
                          <SelectItem value="part-time">دوام جزئي</SelectItem>
                          <SelectItem value="contract">عقد</SelectItem>
                          <SelectItem value="remote">عن بُعد</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="level">المستوى</Label>
                      <Select
                        value={newJob.level}
                        onValueChange={(value: Job["level"]) => setNewJob({ ...newJob, level: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="entry">مبتدئ</SelectItem>
                          <SelectItem value="mid">متوسط</SelectItem>
                          <SelectItem value="senior">كبير</SelectItem>
                          <SelectItem value="executive">تنفيذي</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="exp-min">سنوات الخبرة (الحد الأدنى)</Label>
                      <Input
                        id="exp-min"
                        type="number"
                        value={newJob.experienceYears.min}
                        onChange={(e) =>
                          setNewJob({
                            ...newJob,
                            experienceYears: { ...newJob.experienceYears, min: Number(e.target.value) },
                          })
                        }
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <Label htmlFor="exp-max">سنوات الخبرة (الحد الأعلى)</Label>
                      <Input
                        id="exp-max"
                        type="number"
                        value={newJob.experienceYears.max}
                        onChange={(e) =>
                          setNewJob({
                            ...newJob,
                            experienceYears: { ...newJob.experienceYears, max: Number(e.target.value) },
                          })
                        }
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="salary-min">الراتب الأدنى (ريال)</Label>
                      <Input
                        id="salary-min"
                        type="number"
                        value={newJob.salary.min}
                        onChange={(e) =>
                          setNewJob({
                            ...newJob,
                            salary: { ...newJob.salary, min: Number(e.target.value) },
                          })
                        }
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <Label htmlFor="salary-max">الراتب الأعلى (ريال)</Label>
                      <Input
                        id="salary-max"
                        type="number"
                        value={newJob.salary.max}
                        onChange={(e) =>
                          setNewJob({
                            ...newJob,
                            salary: { ...newJob.salary, max: Number(e.target.value) },
                          })
                        }
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">وصف الوظيفة</Label>
                    <Textarea
                      id="description"
                      value={newJob.description}
                      onChange={(e) => setNewJob({ ...newJob, description: e.target.value })}
                      placeholder="أدخل وصف مفصل للوظيفة"
                      rows={4}
                    />
                  </div>

                  <div>
                    <Label>المهارات المطلوبة</Label>
                    <SkillsSelector
                      selectedSkills={newJob.skills}
                      onSkillsChange={(skills) => setNewJob({ ...newJob, skills })}
                      maxSkills={10}
                    />
                  </div>

                  <div>
                    <Label>المميزات والفوائد</Label>
                    <BenefitsSelector
                      selectedBenefits={newJob.benefits}
                      onBenefitsChange={(benefits) => setNewJob({ ...newJob, benefits })}
                      maxBenefits={10}
                    />
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddJob} className="flex-1">
                      إضافة الوظيفة
                    </Button>
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} className="flex-1">
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الوظائف..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="تصفية حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشطة</SelectItem>
                  <SelectItem value="paused">متوقفة</SelectItem>
                  <SelectItem value="closed">مغلقة</SelectItem>
                  <SelectItem value="draft">مسودة</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="تصفية حسب النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="full-time">دوام كامل</SelectItem>
                  <SelectItem value="part-time">دوام جزئي</SelectItem>
                  <SelectItem value="contract">عقد</SelectItem>
                  <SelectItem value="remote">عن بُعد</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Jobs Table - Desktop */}
        <Card className="hidden md:block">
          <CardHeader>
            <CardTitle>الوظائف ({filteredJobs.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الوظيفة</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>الخبرة</TableHead>
                  <TableHead>الراتب</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>المتقدمين</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredJobs.map((job) => (
                  <TableRow key={job.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{job.title}</div>
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {job.locationUrl ? (
                            <a
                              href={job.locationUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {job.location}
                            </a>
                          ) : (
                            job.location
                          )}
                        </div>
                        <div className="text-sm text-gray-500">{job.company}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getTypeColor(job.type)}>{getTypeName(job.type)}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {job.experienceYears.min}-{job.experienceYears.max} سنوات
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()} {job.salary.currency}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select
                        value={job.status}
                        onValueChange={(value: Job["status"]) => handleStatusChange(job.id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <Badge className={getStatusColor(job.status)}>{getStatusName(job.status)}</Badge>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">نشطة</SelectItem>
                          <SelectItem value="paused">متوقفة</SelectItem>
                          <SelectItem value="closed">مغلقة</SelectItem>
                          <SelectItem value="draft">مسودة</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>{job.applicants}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" onClick={() => handleViewJob(job)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleEditJob(job)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteJob(job.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Jobs Cards - Mobile */}
        <div className="md:hidden space-y-4">
          {filteredJobs.map((job) => (
            <Card key={job.id}>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-sm">{job.title}</h3>
                      <p className="text-sm text-gray-600">{job.company}</p>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <MapPin className="h-3 w-3" />
                        {job.locationUrl ? (
                          <a
                            href={job.locationUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {job.location}
                          </a>
                        ) : (
                          job.location
                        )}
                      </div>
                    </div>
                    <Badge className={getStatusColor(job.status)}>{getStatusName(job.status)}</Badge>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <Badge className={getTypeColor(job.type)}>{getTypeName(job.type)}</Badge>
                    <Badge variant="outline">{getLevelName(job.level)}</Badge>
                    <Badge variant="outline">
                      {job.experienceYears.min}-{job.experienceYears.max} سنوات
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()} {job.salary.currency}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {job.applicants} متقدم
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 bg-transparent"
                      onClick={() => handleViewJob(job)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      عرض
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleEditJob(job)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteJob(job.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredJobs.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">لا توجد وظائف</h3>
              <p className="text-gray-600">لم يتم العثور على وظائف مطابقة للبحث</p>
            </CardContent>
          </Card>
        )}

        {/* View Job Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{viewingJob?.title}</DialogTitle>
              <DialogDescription>{viewingJob?.company}</DialogDescription>
            </DialogHeader>
            {viewingJob && (
              <div className="space-y-6">
                {/* Job Info */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-500">الموقع</div>
                    <div className="text-sm">
                      {viewingJob.locationUrl ? (
                        <a
                          href={viewingJob.locationUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline flex items-center gap-1"
                        >
                          <MapPin className="h-3 w-3" />
                          {viewingJob.location}
                        </a>
                      ) : (
                        viewingJob.location
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">النوع</div>
                    <Badge className={getTypeColor(viewingJob.type)}>{getTypeName(viewingJob.type)}</Badge>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">المستوى</div>
                    <div className="text-sm">{getLevelName(viewingJob.level)}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">سنوات الخبرة</div>
                    <div className="text-sm">
                      {viewingJob.experienceYears.min}-{viewingJob.experienceYears.max} سنوات
                    </div>
                  </div>
                </div>

                {/* Salary */}
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-2">الراتب</div>
                  <div className="text-lg font-semibold text-green-600">
                    {viewingJob.salary.min.toLocaleString()} - {viewingJob.salary.max.toLocaleString()}{" "}
                    {viewingJob.salary.currency}
                  </div>
                </div>

                {/* Description */}
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-2">وصف الوظيفة</div>
                  <p className="text-gray-700 dark:text-gray-300">{viewingJob.description}</p>
                </div>

                {/* Requirements */}
                {viewingJob.requirements.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-gray-500 mb-2">المتطلبات</div>
                    <ul className="list-disc list-inside space-y-1">
                      {viewingJob.requirements.map((req, index) => (
                        <li key={index} className="text-gray-700 dark:text-gray-300">
                          {req}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Skills */}
                {viewingJob.skills.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-gray-500 mb-2">المهارات المطلوبة</div>
                    <div className="flex flex-wrap gap-2">
                      {viewingJob.skills.map((skill, index) => (
                        <Badge key={index} variant="outline">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Benefits */}
                {viewingJob.benefits.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-gray-500 mb-2">المميزات والفوائد</div>
                    <div className="flex flex-wrap gap-2">
                      {viewingJob.benefits.map((benefit, index) => (
                        <Badge key={index} variant="secondary">
                          {benefit}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">{viewingJob.applicants}</div>
                    <div className="text-sm text-gray-500">متقدم</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">{viewingJob.views}</div>
                    <div className="text-sm text-gray-500">مشاهدة</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {new Date(viewingJob.postedDate).toLocaleDateString("ar-SA")}
                    </div>
                    <div className="text-sm text-gray-500">تاريخ النشر</div>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Edit Job Dialog */}
        <Dialog open={!!editingJob} onOpenChange={() => setEditingJob(null)}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>تعديل الوظيفة</DialogTitle>
              <DialogDescription>تعديل تفاصيل الوظيفة</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-title">عنوان الوظيفة</Label>
                  <Input
                    id="edit-title"
                    value={newJob.title}
                    onChange={(e) => setNewJob({ ...newJob, title: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-company">الشركة</Label>
                  <Input
                    id="edit-company"
                    value={newJob.company}
                    onChange={(e) => setNewJob({ ...newJob, company: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-location">الموقع</Label>
                  <Input
                    id="edit-location"
                    value={newJob.location}
                    onChange={(e) => setNewJob({ ...newJob, location: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-locationUrl">رابط الموقع</Label>
                  <Input
                    id="edit-locationUrl"
                    value={newJob.locationUrl}
                    onChange={(e) => setNewJob({ ...newJob, locationUrl: e.target.value })}
                    placeholder="https://maps.google.com/?q=..."
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="edit-description">وصف الوظيفة</Label>
                <Textarea
                  id="edit-description"
                  value={newJob.description}
                  onChange={(e) => setNewJob({ ...newJob, description: e.target.value })}
                  rows={4}
                />
              </div>

              <div>
                <Label>المهارات المطلوبة</Label>
                <SkillsSelector
                  selectedSkills={newJob.skills}
                  onSkillsChange={(skills) => setNewJob({ ...newJob, skills })}
                  maxSkills={10}
                />
              </div>

              <div>
                <Label>المميزات والفوائد</Label>
                <BenefitsSelector
                  selectedBenefits={newJob.benefits}
                  onBenefitsChange={(benefits) => setNewJob({ ...newJob, benefits })}
                  maxBenefits={10}
                />
              </div>

              <div className="flex gap-2 pt-4">
                <Button onClick={handleUpdateJob} className="flex-1">
                  حفظ التغييرات
                </Button>
                <Button variant="outline" onClick={() => setEditingJob(null)} className="flex-1">
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}
